# 使用服务器上可能已有的基础镜像
FROM alpine:latest

# 如果alpine:latest不存在，可以尝试以下替代方案：
# FROM ubuntu:20.04
# FROM centos:7

WORKDIR /var/www/html

# 安装必要的包（如果网络不通，这步可能也会失败）
# 我们需要检查服务器上是否有离线包管理方案

# 创建必要的目录和用户
RUN addgroup -g 82 -S www-data && \
    adduser -u 82 -D -S -G www-data www-data

# 创建必要的目录
RUN mkdir -p /var/www/html/storage /var/www/html/bootstrap/cache /var/log/nginx /var/lib/nginx && \
    chown -R www-data:www-data /var/www/html && \
    chmod -R 755 /var/www/html && \
    chmod -R 777 /var/www/html/storage && \
    chmod -R 777 /var/www/html/bootstrap/cache

# 复制应用文件
COPY . /var/www/html/

# 设置权限
RUN chown -R www-data:www-data /var/www/html

EXPOSE 80

# 简单的启动脚本
COPY docker-entrypoint-offline.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint-offline.sh

ENTRYPOINT ["docker-entrypoint-offline.sh"]
CMD ["tail", "-f", "/dev/null"]
