FROM richarvey/nginx-php-fpm:3.1.6

WORKDIR /var/www/html

# 安装必要的包和PHP扩展
RUN apk add --no-cache \
    git \
    curl \
    libpng-dev \
    libxml2-dev \
    zip \
    unzip \
    oniguruma-dev \
    nodejs \
    npm \
    dcron \
    busybox-suid \
    mysql-client \
    redis \
    && docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd \
    && curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# 创建必要的目录和设置权限
RUN mkdir -p /var/www/html/storage \
             /var/www/html/bootstrap/cache \
             /var/www/html/public \
             /var/log/cron \
    && chown -R nginx:nginx /var/www/html \
    && chmod -R 755 /var/www/html

# 复制应用代码
COPY . /var/www/html/

# 复制配置文件
COPY nginx-default.conf /etc/nginx/http.d/default.conf
COPY zzz-custom.conf /usr/local/etc/php-fpm.d/zzz-custom.conf

# PHP配置优化
RUN echo "upload_max_filesize=50M" >> /usr/local/etc/php/conf.d/docker-php-ext-upload.ini \
    && echo "post_max_size=50M" >> /usr/local/etc/php/conf.d/docker-php-ext-upload.ini \
    && echo "memory_limit=512M" >> /usr/local/etc/php/conf.d/docker-php-ext-upload.ini \
    && echo "max_execution_time=300" >> /usr/local/etc/php/conf.d/docker-php-ext-upload.ini \
    && echo "max_input_vars=3000" >> /usr/local/etc/php/conf.d/docker-php-ext-upload.ini

# 设置最终权限
RUN chown -R nginx:nginx /var/www/html \
    && find /var/www/html -type d -exec chmod 755 {} \; \
    && find /var/www/html -type f -exec chmod 644 {} \; \
    && chmod -R 777 /var/www/html/storage \
    && chmod -R 777 /var/www/html/bootstrap/cache \
    && chmod 644 /var/www/html/public/index.php

EXPOSE 80

# 复制启动脚本
COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

ENTRYPOINT ["docker-entrypoint.sh"]
CMD ["/bin/sh", "-c", "php-fpm -D && nginx -g 'daemon off;'"]
