FROM ubuntu:18.04

# 避免交互式提示
ENV DEBIAN_FRONTEND=noninteractive

# 设置工作目录
WORKDIR /var/www/html

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    curl \
    wget \
    zip \
    unzip \
    supervisor \
    nginx \
    software-properties-common \
    ca-certificates \
    lsb-release \
    apt-transport-https \
    cron

# 添加PHP存储库并安装PHP
RUN add-apt-repository ppa:ondrej/php -y && \
    apt-get update && \
    apt-get install -y \
    php7.4 \
    php7.4-cli \
    php7.4-fpm \
    php7.4-common \
    php7.4-mysql \
    php7.4-zip \
    php7.4-gd \
    php7.4-mbstring \
    php7.4-curl \
    php7.4-xml \
    php7.4-bcmath \
    php7.4-redis

# 清理apt缓存
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# 安装Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# 配置Nginx
COPY docker/nginx/default.conf /etc/nginx/sites-available/default

# 配置PHP
COPY docker/php/php.ini /etc/php/7.4/fpm/conf.d/custom.ini
COPY docker/php/php.ini /etc/php/7.4/cli/conf.d/custom.ini

# 配置Supervisor
COPY docker/supervisor/web.conf /etc/supervisor/conf.d/supervisord.conf

# 创建启动脚本
COPY docker/web-start.sh /start.sh
RUN chmod +x /start.sh

# 设置权限
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www

# 暴露端口
EXPOSE 80

# 配置Cron
RUN echo "* * * * * cd /var/www/html && php artisan schedule:run >> /dev/null 2>&1" > /etc/cron.d/laravel-scheduler
RUN chmod 0644 /etc/cron.d/laravel-scheduler
RUN crontab /etc/cron.d/laravel-scheduler

# 启动服务
CMD ["/start.sh"]
