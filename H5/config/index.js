const CONFIG = {
    //开发环境配置
    development: {
        // HTTP_REQUEST_URL:'http://yyt-backend.qingniaokx.com', // 测试
        // HTTP_REQUEST_URL:'http://sass-test4.qingniaokx.com', // 测试
        // HTTP_REQUEST_URL:'http://laozhangren.eu-1.sharedwithexpose.com', // 正式
			HTTP_REQUEST_URL:'http://82.156.98.152:8081', 
		// HTTP_REQUEST_URL:'https://yyadmin.yxgmw.com', // 打包
        // HTTP_HTML_URL:'http://dev2-h5.qingniaokx.com/', // 前端测试域名
        // HTTP_HTML_URL:'https://yyt-h5.yunonemeta.art/', // 前端正式域名
			HTTP_HTML_URL:'https://h5.yunonemeta.art/', // 打包正式域名
        // 请求头
        HEADER:{
          'Content-Type': 'application/json'
        },
        // 会话密钥名称 
        TOKENNAME: 'Authorization',
        //用户信息缓存名称  
        CACHE_USERINFO:'USERINFO',
        //token缓存名称
        CACHE_TOKEN:'accessToken',
        //token过期事件
        CACHE_EXPIRES_TIME:'EXPIRES_TIME',
        //模板缓存
        CACHE_SUBSCRIBE_MESSAGE:'SUBSCRIBE_MESSAGE',
        //用户信息缓存名称
        CACHE_USERINFO: 'USERINFO',
    },
    //生产环境配置
    production: {
        // HTTP_REQUEST_URL:'http://yyt-backend.qingniaokx.com', // 测试
		// HTTP_REQUEST_URL:'http://sass-test4.qingniaokx.com', // 测试
        // HTTP_REQUEST_URL:'https://yyt-yjs.yunonemeta.art', // 正式
		  HTTP_REQUEST_URL:'http://82.156.98.152:8081', // 正式
		// HTTP_HTML_URL:'http://dev2-h5.qingniaokx.com/', // 前端测试域名
		// HTTP_HTML_URL:'https://yyt-h5.yunonemeta.art/', // 前端正式域名
		HTTP_HTML_URL:'https://h5.yunonemeta.art/', // 前端正式域名
        // 请求头
        HEADER:{
          'Content-Type': 'application/json'
        },
        // 会话密钥名称 
        TOKENNAME: 'Authorization',
        //用户信息缓存名称  
        CACHE_USERINFO:'USERINFO',
        //token缓存名称
        CACHE_TOKEN:'accessToken',
        //token过期事件
        CACHE_EXPIRES_TIME:'EXPIRES_TIME',
        //模板缓存
        CACHE_SUBSCRIBE_MESSAGE:'SUBSCRIBE_MESSAGE',
        //用户信息缓存名称
        CACHE_USERINFO: 'USERINFO',
    }

};
export default CONFIG[process.env.NODE_ENV];
