{
    "name" : "云忆Meta",
    "appid" : "__UNI__6E111A5",
    "description" : "",
    "versionName" : "1.0.0",
    "versionCode" : 100,
    "transformPx" : false,
    "networkTimeout" : {
        "request" : 30000,
        "uploadFile" : 60000,
        "downloadFile" : 60000
    },
    "app-plus" : {
        "popGesture" : "close",
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "compilerVersion" : 3,
        "safearea" : {
            //安全区域配置，仅iOS平台生效  
            "bottom" : {
                // 底部安全区域配置  
                "offset" : "none" // 底部安全区域偏移，"none"表示不空出安全区域，"auto"自动计算空出安全区域，默认值为"none"  
            }
        },
        "modules" : {
            "Payment" : {}
        },
        "distribute" : {
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\" />",
                    "<uses-permission android:name=\"android.permission.CAPTURE_VIDEO_OUTPUT\" />",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.REQUEST_INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ],
                "targetSdkVersion" : 30,
                "minSdkVersion" : 21,
                "autoSdkPermissions" : false
            },
            "ios" : {
                "UIBackgroundModes" : "",
                "urltypes" : "uni-8jbook",
                "capabilities" : {
                    "entitlements" : {
                        "com.apple.developer.associated-domains" : [ "applinks:8jbook.com" ]
                    }
                },
                "privacyDescription" : {
                    "NSPhotoLibraryUsageDescription" : "云壹Meta需要您的同意,才能访问相册，需要开启相册权限，是否允许用户上传头像时访问你的相册？",
                    "NSPhotoLibraryAddUsageDescription" : "云壹Meta需要您的同意,才能访问相册，需要开启相册权限，是否允许用户上传头像时访问你的相机？",
                    "NSCameraUsageDescription" : "云壹Meta需要您的同意,才能访问相机，以便拍摄上传图片",
                    "NSMicrophoneUsageDescription" : "云壹Meta需要访问你的麦克风，以便语音播放",
                    "NSLocationWhenInUseUsageDescription" : "云壹Meta需要访问你的地理位置，以便为你提供当前位置信息",
                    "NSLocationAlwaysUsageDescription" : "云壹Meta需要访问你的地理位置，以便为你提供当前位置信息",
                    "NSLocationAlwaysAndWhenInUseUsageDescription" : "云壹Meta需要访问你的地理位置，以便为你提供当前位置信息"
                },
                "idfa" : false,
                "dSYMs" : false
            },
            "sdkConfigs" : {
                "ad" : {},
                "share" : {
                    "weixin" : {
                        "appid" : "wx8e59e935078abf10",
                        "UniversalLinks" : "https://8jbook.com/ulink/"
                    }
                },
                "oauth" : {
                    "apple" : {},
                    "weixin" : {
                        "appid" : "wx8e59e935078abf10",
                        "appsecret" : "wx8e59e935078abf10",
                        "UniversalLinks" : "https://8jbook.com/ulink/"
                    }
                },
                "statics" : {
                    "umeng" : {
                        "appkey_ios" : "5f13fe01dbc2ec081356decc",
                        "channelid_ios" : "8jbook-ios",
                        "appkey_android" : "5f13febf978eea08cad18f23",
                        "channelid_android" : "8jbook-android"
                    }
                },
                "payment" : {
                    "alipay" : {
                        "__platform__" : [ "ios", "android" ]
                    },
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "wxe0482afbe5ecf424",
                        "UniversalLinks" : "https://8jbook.com/ulink/"
                    }
                }
            },
            "splashscreen" : {
                "android" : {
                    "hdpi" : "",
                    "xhdpi" : "",
                    "xxhdpi" : ""
                },
                "ios" : {
                    "iphone" : {
                        "portrait-896h@3x" : "",
                        "landscape-896h@3x" : "",
                        "portrait-896h@2x" : "",
                        "landscape-896h@2x" : "",
                        "iphonex" : "",
                        "iphonexl" : "",
                        "retina55" : "",
                        "retina55l" : "",
                        "retina47" : "",
                        "retina47l" : "",
                        "retina40" : "",
                        "retina40l" : "",
                        "retina35" : ""
                    },
                    "ipad" : {
                        "portrait-1366h@2x" : "",
                        "landscape-1366h@2x" : "",
                        "portrait-1194h@2x" : "",
                        "landscape-1194h@2x" : "",
                        "portrait-1112h@2x" : ""
                    }
                },
                "useOriginalMsgbox" : true
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            }
        },
        "splashscreen" : {
            "waiting" : false,
            "alwaysShowBeforeRender" : false
        },
        "nvueLaunchMode" : "",
        "uniStatistics" : {
            "enable" : true,
            "version" : "2"
        },
        "nativePlugins" : {}
    },
    "quickapp" : {},
    "mp-weixin" : {
        "appid" : "wx19ff4a0670970407",
        "setting" : {
            "urlCheck" : false,
            "postcss" : true,
            "minified" : true
        },
        "permission" : {},
        "uniStatistics" : {
            "enable" : true
        }
    },
    "h5" : {
        "router" : {
            "mode" : "history"
        },
        "optimization" : {
            "treeShaking" : {
                "enable" : true
            }
        },
        "async" : {
            "timeout" : 10000
        },
        "devServer" : {
            "https" : false
        },
        "uniStatistics" : {
            "enable" : true
        },
        "template" : "h5.html"
    },
    "uniStatistics" : {
        "enable" : true,
        "version" : "1"
    },
    "mp-alipay" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-baidu" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-qq" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-toutiao" : {
        "uniStatistics" : {
            "enable" : true
        },
        "appid" : "tt42383485dd07b1b601",
        "setting" : {
            "urlCheck" : false
        }
    },
    "mp-jd" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-kuaishou" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-lark" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "quickapp-webview-huawei" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "quickapp-webview-union" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "css" : {
        "sassImplementationName" : "node-sass"
    }
}
