<template>
  <view class="container" @click="handleUserInteraction" @touchstart="handleUserInteraction" @scroll="handleUserInteraction">
    <!-- 音乐播放提示遮罩 -->
    <view class="music-tip-mask" v-if="showMusicTip" @click.stop="startMusicAndHideTip">
    </view>

    <!-- 添加页面级分享按钮 -->
    <view class="button-container">
      <view class="music-selector">
        <picker @change="handleMusicChange" :value="selectedMusicIndex" :range="musicList" range-key="name" class="music-picker">
          <text class="music-label">背景音乐: </text>
          <text class="music-value">{{ selectedMusic ? selectedMusic.name : '请选择' }}</text>
        </picker>
        <button class="music-control-btn" @click="toggleMusic">
          <text class="music-control-icon">{{ isPlaying ? '⏸' : '▶' }}</text>
        </button>
      </view>
      <view class="button-group">
        <button class="share-btn" @click="handleShare">分享</button>
      </view>
    </view>

    <view class="album-list">
      <view v-for="(item, index) in photoList.items" :key="index" class="album-item">
        <!-- 待处理状态 -->
        <view v-if="isProcessingStatus(item.status)" class="photo-container">
          <image
            :src="item.cover_image_url || 'https://placehold.co/300x300/333/ffffff?text=Processing'"
            mode="aspectFill"
            class="photo"
          />
          <view class="processing-mask">
            <view class="processing-content">
              <text class="processing-text">预估需要1-10分钟</text>
              <text class="processing-text">请耐心等待...</text>
              <view class="spinner-container">
                <view class="spinner"></view>
              </view>
              <text class="processing-status">处理中</text>
            </view>
          </view>
        </view>
        <!-- 成功状态 -->
        <view v-else-if="item.status === 'success'" class="photo-container">
          <view
            v-if="!item.videoError"
            class="photo-wrapper"
            @click.stop="openVideoModal(item)"
          >
            <image
              :src="item.cover_image_url"
              class="photo"
              mode="aspectFill"
            />
            <!-- 添加播放按钮覆盖层 -->
            <view class="play-button-overlay">
              <view class="play-button">
                <text class="play-icon">▶</text>
              </view>
            </view>
          </view>
          <view v-else class="error-container">
            <text class="error-text">视频加载失败</text>
            <text v-if="item.errorMessage" class="error-detail">{{ item.errorMessage }}</text>
            <button class="retry-btn" @click="handleRetry(item)">重试</button>
            <button class="regenerate-btn" @click="regenerateVideo(item)">重新生成</button>
          </view>

          <!-- 视频删除按钮 -->
          <button
            class="video-delete-btn"
            @click="confirmDelete(item)"
          >
            <text class="delete-icon">×</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="photoList.items && photoList.items.length === 0" class="empty-state">
      <text class="empty-text">暂无视频</text>
    </view>

    <!-- 播放弹窗 -->
    <view class="video-modal" v-if="showVideoModal" @click.stop="closeVideoModal">
      <view class="modal-content" @click.stop>
        <view class="modal-video-container">
          <video
            :id="'video-player-' + currentVideo.id"
            :src="currentVideo.video_url"
            :poster="currentVideo.cover_image_url"
            class="modal-video"
            :controls="true"
            :enable-progress-gesture="true"
            :show-play-btn="true"
            :show-center-play-btn="true"
            :show-fullscreen-btn="true"
            object-fit="contain"
            style="width: 100%; height: 90%;"
            @error="handleModalVideoError"
            @ended="handleModalVideoEnded"
            @play="handleModalVideoPlay"
            @pause="handleModalVideoPause"
            @fullscreenchange="handleFullscreenChange"
          ></video>

          <!-- 全屏分享按钮 -->
          <view v-if="isFullscreenMode" class="fullscreen-share-btn" @click.stop="handleFullscreenShare">
            <text class="fullscreen-share-icon">分享视频</text>
          </view>

          <!-- 视频加载指示器 -->
          <view v-if="isModalVideoLoading" class="video-loading-indicator">
            <view class="loading-spinner"></view>
            <text class="loading-text">视频加载中...</text>
          </view>
        </view>

        <!-- 视频操作按钮 -->
        <view class="modal-buttons">
          <button class="modal-btn share-btn" @click="handleModalShare">
            <text class="btn-text">分享</text>
          </button>
          <button class="modal-btn regenerate-btn" @click="regenerateVideo(currentVideo)">
            <text class="btn-text">重新生成</text>
          </button>
        </view>

        <!-- 关闭按钮 -->
        <view class="modal-close" @click="closeVideoModal">
          <text class="close-icon">×</text>
        </view>
      </view>
    </view>

    <new-footer-menu :active="'album'" />
  </view>
</template>

<script>
import config from '@/config/index.js'

// 全局音乐配置对象
const music_config = {bgmusic: null}

export default {
  data() {
    return {
      photoList: {
        total: 0,
        items: [],
        page: 1,
        pageSize: 10,
        hasMore: true
      },
      musicList: [],
      selectedMusicIndex: 0,
      selectedMusic: null,
      isPlaying: false,
      hasUserInteracted: false,
      showMusicTip: false, // 修改为false，默认不显示遮罩层
      hasLogin: false,
      userInfo: null,
      progressTimers: {},
      showVideoModal: false,
      currentVideo: null,
      isModalVideoLoading: false,
      modalVideoPlaybackStarted: false,
      fullscreenCheckTimer: null,
      isFullscreenMode: false,
      isRefreshing: false,
      networkStatus: true,
      preloadQueue: [],
      isPreloading: false
    }
  },

  onLoad(options) {
    console.log('相册页面 onLoad 触发，参数:', options)
    this.initNetworkListener();
    this.checkLoginStatus();
  },

  onShow() {
    console.log('相册页面 onShow 触发');
    this.checkLoginStatus();
    // 添加自动播放
    this.autoPlayBackgroundMusic();
  },

  onHide() {
    this.cleanupResources();
  },

  onUnload() {
    this.cleanupResources();
  },

  onPullDownRefresh() {
    this.refreshAlbumData();
  },

  onReachBottom() {
    if (this.photoList.hasMore && !this.isRefreshing) {
      this.loadMorePhotos();
    }
  },

  onError(error) {
    console.error('页面错误:', error);
    uni.showToast({
      title: '页面加载失败，请重试',
      icon: 'none',
      duration: 2000
    });
  },

  onShareAppMessage(res) {
    // 获取当前用户ID
    const userInfo = uni.getStorageSync('userInfo') || {}
    const customerId = userInfo.id

    // 如果是从视频点击分享，则分享单个视频
    if (res.from === 'button' && res.target && res.target.dataset && res.target.dataset.id) {
      const videoId = res.target.dataset.id
      // 生成分享token并保存
      const shareToken = this.generateShareToken()
      // 设置过期时间为3天
      const expireTime = Date.now() + (3 * 24 * 60 * 60 * 1000)
      uni.setStorageSync(`share_token_${videoId}`, { token: shareToken, expireTime })
      return {
        title: '我分享了一个视频，快来看看吧！',
        imageUrl: res.target.dataset.cover,
        path: `/pages/share/index?video_id=${videoId}&token=${shareToken}`
      }
    }

    // 否则分享整个相册
    // 生成分享token并保存
    const shareToken = this.generateShareToken()
    // 设置过期时间为3天
    const expireTime = Date.now() + (3 * 24 * 60 * 60 * 1000)
    uni.setStorageSync(`share_token_${customerId}`, { token: shareToken, expireTime })

    return {
      title: '我的视频相册，快来看看吧！',
      imageUrl: this.photoList.items[0]?.cover_image_url,
      path: `/pages/share/index?user_id=${customerId}&token=${shareToken}`
    }
  },

  methods: {
    // 初始化网络监听
    initNetworkListener() {
      uni.getNetworkType({
        success: (res) => {
          this.networkStatus = res.networkType !== 'none';
        }
      });

      uni.onNetworkStatusChange((res) => {
        this.networkStatus = res.isConnected;
        if (res.isConnected) {
          this.refreshAlbumData();
        } else {
          uni.showToast({
            title: '网络连接已断开',
            icon: 'none',
            duration: 2000
          });
        }
      });
    },

    // 清理资源
    cleanupResources() {
      if (music_config.bgmusic) {
        music_config.bgmusic.destroy();
        music_config.bgmusic = null;
        this.isPlaying = false;
      }
      this.clearPreloadQueue();
    },

    // 清理预加载队列
    clearPreloadQueue() {
      this.preloadQueue = [];
      this.isPreloading = false;
    },

    // 加载更多照片
    loadMorePhotos() {
      if (!this.photoList.hasMore || this.isRefreshing) return;

      this.isRefreshing = true;
      const nextPage = this.photoList.page + 1;

      uni.request({
        url: `${config.HTTP_REQUEST_URL}/api/videos`,
        method: 'GET',
        data: {
          page: nextPage,
          page_size: this.photoList.pageSize
        },
        header: {
          'Authorization': `Bearer ${uni.getStorageSync('accessToken')}`
        },
        success: (res) => {
          if (res.statusCode === 200) {
            const newItems = res.data.items || [];
            if (newItems.length > 0) {
              this.photoList.items = [...this.photoList.items, ...newItems];
              this.photoList.page = nextPage;
              this.photoList.hasMore = newItems.length === this.photoList.pageSize;
              this.preloadImages(newItems);
            } else {
              this.photoList.hasMore = false;
            }
          }
        },
        complete: () => {
          this.isRefreshing = false;
        }
      });
    },

    // 预加载图片
    preloadImages(items) {
      items.forEach(item => {
        if (item.cover_image_url) {
          this.preloadQueue.push(item.cover_image_url);
        }
      });
      this.processPreloadQueue();
    },

    // 处理预加载队列
    processPreloadQueue() {
      if (this.isPreloading || this.preloadQueue.length === 0) return;

      this.isPreloading = true;
      const url = this.preloadQueue.shift();

      uni.getImageInfo({
        src: url,
        success: () => {
          console.log('图片预加载成功:', url);
        },
        fail: (error) => {
          console.error('图片预加载失败:', url, error);
        },
        complete: () => {
          this.isPreloading = false;
          if (this.preloadQueue.length > 0) {
            setTimeout(() => this.processPreloadQueue(), 100);
          }
        }
      });
    },

    // 优化后的自动播放背景音乐
    autoPlayBackgroundMusic() {
      if (this.isPlaying) {
        console.log('音乐已经在播放中');
        return;
      }

      setTimeout(() => {
        if (this.selectedMusic) {
          console.log('准备音乐:', this.selectedMusic.name);
          if (!music_config.bgmusic) {
            this.createAudio(this.selectedMusic.url);
            // 尝试自动播放
            this.playMusic();
          }
        } else {
          console.log('没有选择的音乐，尝试加载默认音乐');
          if (this.musicList && this.musicList.length > 0) {
            this.handleMusicChange({
              detail: {
                value: 0
              }
            });
            // 尝试自动播放
            this.playMusic();
          }
        }
      }, 500);
    },

    // 优化后的加载照片列表
    loadPhotoList(showLoading = false) {
      // 首先检查登录状态
      if (!this.hasLogin) {
        console.log('用户未登录，不加载照片列表');
        return;
      }

      if (showLoading) {
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
      }

      if (!this.networkStatus) {
        uni.showToast({
          title: '网络连接已断开',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 检查是否有访问令牌
      const token = uni.getStorageSync('accessToken');
      if (!token) {
        console.log('未找到访问令牌，重新检查登录状态');
        this.checkLoginStatus();
        return;
      }

      uni.request({
        url: `${config.HTTP_REQUEST_URL}/api/videos`,
        method: 'GET',
        data: {
          page: 1,
          page_size: this.photoList.pageSize
        },
        header: {
          'Authorization': `Bearer ${token}`
        },
        success: (res) => {
          if (res.statusCode === 200) {
            this.photoList = {
              ...res.data,
              page: 1,
              pageSize: this.photoList.pageSize,
              hasMore: (res.data.items || []).length === this.photoList.pageSize
            };

            // 重置视频错误状态
            if (this.photoList.items) {
              this.photoList.items.forEach(item => {
                item.videoError = false;
                item.errorMessage = '';
                item.retryCount = 0;
              });
            }

            // 预加载图片
            this.preloadImages(this.photoList.items);
          } else if (res.statusCode === 401) {
            // 未授权，重新检查登录状态
            console.log('访问令牌无效，重新检查登录状态');
            this.checkLoginStatus();
          } else {
            this.handleLoadError('获取视频列表失败');
          }
        },
        fail: (error) => {
          this.handleLoadError('获取视频列表请求失败', error);
        },
        complete: () => {
          uni.hideLoading();
          uni.stopPullDownRefresh();
        }
      });
    },

    // 处理加载错误
    handleLoadError(message, error = null) {
      console.error(message, error);
      uni.showToast({
        title: message,
        icon: 'none',
        duration: 2000
      });
    },

    // 刷新相册数据
    refreshAlbumData() {
      if (this.isRefreshing) return;
      
      // 检查登录状态
      if (!this.hasLogin) {
        console.log('用户未登录，不刷新数据');
        return;
      }
      
      console.log('刷新相册数据');
      this.photoList.page = 1;
      this.photoList.hasMore = true;
      this.loadPhotoList(true);
    },

    // 检查用户登录状态
    checkLoginStatus() {
      this.hasLogin = this.$mStore.getters.hasLogin;
      if (this.hasLogin) {
        getApp().globalData.getUserInfo();
        this.userInfo = uni.getStorageSync('userInfo');
        console.log('用户信息:', this.userInfo);
        // 已登录，加载数据
        this.refreshAlbumData();
        // 加载音乐列表
        this.loadMusicList();
        uni.$emit('albumPageShow');
      } else {
        console.log('用户未登录，跳转到登录页面');
        // 未登录，跳转到登录页面
        this.$mHelper.navigateTo('/pages/user/login/index');
      }
    },

    // 获取音乐列表
    loadMusicList() {
      const token = uni.getStorageSync('accessToken');
      if (!token) {
        console.log('未登录，不加载音乐列表');
        return;
      }
      
      uni.request({
        url: `${config.HTTP_REQUEST_URL}/api/musics/bgm-list`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${token}`
        },
        success: (res) => {
          console.log('音乐列表API响应:', res);
          if (res.statusCode === 200) {
            // 检查返回的数据格式
            const musicData = Array.isArray(res.data) ? res.data : [res.data];
            console.log('处理后的音乐数据:', musicData);
            
            if (musicData && musicData.length > 0) {
              // 适配API返回的数据格式
              this.musicList = musicData.map(music => ({
                id: music.id,
                name: music.title || music.name,
                url: music.music_url || music.url,
                cover: music.cover_image || music.cover
              }));

              console.log('处理后的音乐列表:', this.musicList);
              
              // 选择第一首音乐
              this.selectedMusicIndex = 0;
              this.selectedMusic = this.musicList[0];
              
              // 创建音乐播放器
              this.createAudio(this.selectedMusic.url);
            } else {
              console.log('音乐列表为空，使用默认音乐');
              this.useDefaultMusic();
            }
          } else {
            console.error('获取音乐列表失败，状态码:', res.statusCode);
            this.useDefaultMusic();
          }
        },
        fail: (error) => {
          console.error('获取音乐列表请求失败:', error);
          this.useDefaultMusic();
        }
      });
    },

    // 使用默认音乐
    useDefaultMusic() {
      console.log('使用默认音乐');
      const defaultMusic = {
        id: 0,
        name: '默认背景音乐',
        url: 'https://bjetxgzv.cdn.bspapp.com/VKCEYUGU-uni-app-doc/a4b0600d-5e1f-4946-a280-c8975c2d5556.mp3',
        cover: 'https://bjetxgzv.cdn.bspapp.com/VKCEYUGU-uni-app-doc/7fbf2e60-60eb-4cb6-b6b1-4335f6f24d1a.jpg'
      };

      // 检查默认音乐是否已在列表中
      const existingIndex = this.musicList.findIndex(m => m.id === 0);
      if (existingIndex === -1) {
        // 添加到列表开头
        this.musicList.unshift(defaultMusic);
      }

      // 选择默认音乐
      this.selectedMusicIndex = 0;
      this.selectedMusic = this.musicList[0];

      // 创建音频播放器并自动播放
      this.createAudio(this.selectedMusic.url);
    },

    // 处理音乐选择
    handleMusicChange(e) {
      try {
        console.log('选择音乐:', e.detail.value);

        // 检查索引是否有效
        if (e.detail.value < 0 || e.detail.value >= this.musicList.length) {
          console.error('无效的音乐索引:', e.detail.value);
          uni.showToast({
            title: '选择的音乐无效',
            icon: 'none',
            duration: 2000
          });
          return;
        }

        this.selectedMusicIndex = e.detail.value;
        this.selectedMusic = this.musicList[this.selectedMusicIndex];

        console.log('已选择音乐:', this.selectedMusic);

        // 检查音乐URL是否有效
        if (!this.selectedMusic || !this.selectedMusic.url) {
          console.error('选择的音乐URL无效');
          uni.showToast({
            title: '选择的音乐无效',
            icon: 'none',
            duration: 2000
          });
          return;
        }

        // 创建音乐播放器
        this.createAudio(this.selectedMusic.url);
        // 标记用户已交互，允许自动播放
        this.hasUserInteracted = true;

        // 保存用户的音乐选择
        this.setUserMusic(this.selectedMusic.id);
      } catch (error) {
        console.error('处理音乐选择失败:', error);
        uni.showToast({
          title: '选择音乐失败',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 创建音频播放器
    createAudio(mp3_url) {
      console.log('创建音频播放器，URL:', mp3_url);

      // 检查URL是否有效
      if (!mp3_url) {
        console.error('无效的音频URL');
        return;
      }

      // 检查是否是相对路径格式
      const isRelativePath = mp3_url.match(/^bgm\/\d{8}\/[a-f0-9]+\.mp3$/i);

      if (isRelativePath) {
        // 如果是相对路径格式，直接使用，不需要修改
        console.log('检测到相对路径格式的音乐URL:', mp3_url);

        // 构建完整的URL
        const baseUrl = config.HTTP_REQUEST_URL;
        if (!mp3_url.startsWith('http')) {
          mp3_url = `${baseUrl}/${mp3_url}`;
          console.log('构建完整URL:', mp3_url);
        }
      }

      // 完全销毁当前的音频上下文
      if (music_config.bgmusic) {
        try {
          music_config.bgmusic.stop();
          music_config.bgmusic.destroy();
          music_config.bgmusic = null;
        } catch (error) {
          console.error('销毁音频上下文失败:', error);
        }
      }

      // 创建新的音频上下文
      try {
        music_config.bgmusic = uni.createInnerAudioContext();
        music_config.bgmusic.autoplay = true; // 设置为自动播放
        music_config.bgmusic.loop = true; // 设置循环播放

        // 添加事件监听
        music_config.bgmusic.onCanplay(() => {
          console.log('音频可以播放了');
          // 在音频可以播放时立即尝试播放
          this.playMusic();
        });

        music_config.bgmusic.onPlay(() => {
          console.log('音乐开始播放');
          this.isPlaying = true;
          this.showMusicTip = false; // 播放成功后隐藏提示
        });

        music_config.bgmusic.onError((res) => {
          console.error('音乐播放错误:', res);
          this.isPlaying = false;
          this.showMusicTip = true; // 播放失败时显示提示
          // 错误时尝试重新播放
          setTimeout(() => {
            this.playMusic();
          }, 1000);
        });

        // 设置音频源
        music_config.bgmusic.src = mp3_url;
        console.log('音频上下文创建成功');

        // 检查是否在微信环境中
        if (this.$mHelper.isWX()) {
          let u = navigator.userAgent;
          let isAndroid = u.indexOf('Android') > -1 || u.indexOf('Linux') > -1;
          
          // 微信配置
          wx_config({
            url: window.location.href
          }).then(wx_res => {
            wx.config(wx_res.data);
            wx.ready(() => {
              wx.invoke('getNetworkType', {}, () => {
                // 尝试播放音乐
                this.playMusic();
              });
            });
          }).catch(err => {
            console.error('微信配置失败:', err);
            // 配置失败时也尝试播放
            this.playMusic();
          });
        } else {
          // 非微信环境直接尝试播放
          this.playMusic();
        }
      } catch (error) {
        console.error('创建音频上下文失败:', error);
        // 创建失败时延迟重试
        setTimeout(() => {
          this.createAudio(mp3_url);
        }, 1000);
      }
    },

    // 播放音乐
    playMusic() {
      if (!music_config.bgmusic) {
        console.error('音频上下文不存在，无法播放');
        return;
      }

      // 确保音频URL已设置
      if (!music_config.bgmusic.src) {
        console.error('音频URL未设置，无法播放');
        return;
      }

      // 检查用户是否已交互
      if (!this.hasUserInteracted) {
        console.log('用户尚未与页面交互，等待用户交互后再播放');
        this.showMusicTip = true;
        return;
      }

      try {
        console.log('尝试播放音乐...');
        music_config.bgmusic.play();
        this.isPlaying = true;
        this.showMusicTip = false;
      } catch (error) {
        console.error('播放音乐失败:', error);
        this.isPlaying = false;
        this.showMusicTip = true;
        
        if (error.name === 'NotAllowedError') {
          console.log('需要用户交互才能播放音频');
          uni.showToast({
            title: '点击页面任意位置开始播放音乐',
            icon: 'none',
            duration: 2000
          });
        } else {
          // 尝试重新加载音乐
          setTimeout(() => {
            if (music_config.bgmusic) {
              try {
                music_config.bgmusic.stop();
                music_config.bgmusic.play();
                this.isPlaying = true;
                this.showMusicTip = false;
              } catch (retryError) {
                console.error('重试播放失败:', retryError);
                this.showMusicTip = true;
                // 如果重试失败，再次尝试创建音频上下文
                if (this.selectedMusic && this.selectedMusic.url) {
                  setTimeout(() => {
                    this.createAudio(this.selectedMusic.url);
                  }, 1000);
                }
              }
            }
          }, 1000);
        }
      }
    },

    // 切换音乐播放状态
    toggleMusic() {
      if (!this.selectedMusic) return;

      // 标记用户已交互
      this.hasUserInteracted = true;

      if (!music_config.bgmusic && !this.isPlaying) {
        // 如果没有音乐播放器，创建一个
        this.createAudio(this.selectedMusic.url);
      } else if (music_config.bgmusic && !this.isPlaying) {
        // 如果有音乐播放器但没有播放，开始播放
        this.playMusic();
      } else {
        // 如果正在播放，停止
        try {
          music_config.bgmusic.pause();
          this.isPlaying = false;
          console.log('音乐已暂停');
        } catch (error) {
          console.error('暂停音乐失败:', error);
          // 如果暂停失败，尝试完全停止
          try {
            music_config.bgmusic.stop();
            this.isPlaying = false;
          } catch (stopError) {
            console.error('停止音乐失败:', stopError);
          }
        }
      }
    },

    // 处理用户交互
    handleUserInteraction() {
      console.log('用户与页面交互');

      // 如果用户已经交互过，不需要再处理
      if (this.hasUserInteracted) return;

      // 标记用户已交互
      this.hasUserInteracted = true;
      console.log('已标记用户交互状态');

      // 确保音乐提示不显示
      this.showMusicTip = false;

      // 延迟一小段时间再尝试播放音乐，确保浏览器已经注册了用户交互
      setTimeout(() => {
        // 如果有音乐，尝试播放
        if (this.selectedMusic && !this.isPlaying) {
          console.log('尝试播放音乐');
          if (music_config.bgmusic) {
            this.playMusic();
          } else {
            // 如果没有音乐播放器，创建一个
            this.createAudio(this.selectedMusic.url);
          }
        }
      }, 300);
    },

    // 点击音乐提示开始播放并隐藏提示
    startMusicAndHideTip() {
      console.log('开始播放音乐并隐藏提示');
      // 隐藏提示
      this.showMusicTip = false;
      // 标记用户已交互
      this.hasUserInteracted = true;
      
      // 确保音频上下文已准备好
      if (music_config.bgmusic) {
        // 先暂停当前播放
        try {
          music_config.bgmusic.pause();
        } catch (e) {
          console.log('暂停音乐失败:', e);
        }
        
        // 延迟一小段时间后开始播放
        setTimeout(() => {
          this.playMusic();
        }, 300);
      } else if (this.selectedMusic) {
        // 如果没有音频上下文但有选中的音乐，创建并播放
        this.createAudio(this.selectedMusic.url);
      }
    },

    // 判断是否为处理中状态
    isProcessingStatus(status) {
      if (!status && status !== 0) return false;

      // 将状态转换为字符串进行比较
      const statusStr = String(status).toLowerCase();

      // 处理中状态的可能值
      const processingStatuses = ['pending', 'processing', '0'];

      return processingStatuses.includes(statusStr) || status === 0;
    },

    // 清理视频URL中的多余参数并添加时间戳
    cleanVideoUrl(url) {
      if (!url) return '';

      try {
        // 移除URL中的hash部分
        let cleanUrl = url.split('#')[0];

        // 先清除可能存在的时间戳参数
        if (cleanUrl.includes('?')) {
          const urlParts = cleanUrl.split('?');
          const baseUrl = urlParts[0];
          const params = new URLSearchParams(urlParts[1]);

          // 删除已有的时间戳参数
          params.delete('_t');

          // 重建URL
          cleanUrl = baseUrl + (params.toString() ? '?' + params.toString() : '');
        }

        // 为所有视频URL添加新的时间戳参数，避免缓存问题
        if (cleanUrl.includes('?')) {
          cleanUrl = `${cleanUrl}&_t=${Date.now()}`;
        } else {
          cleanUrl = `${cleanUrl}?_t=${Date.now()}`;
        }

        return cleanUrl;
      } catch (error) {
        console.error('处理视频URL时出错:', error);

        // 如果处理失败，添加简单的时间戳参数
        if (url.includes('?')) {
          return `${url}&_t=${Date.now()}`;
        } else {
          return `${url}?_t=${Date.now()}`;
        }
      }
    },

    // 处理视频加载错误
    handleVideoError(item, e) {
      console.error('视频加载错误:', item.id, item.video_url, e);

      // 获取详细错误信息
      const errMsg = e ? (e.detail ? e.detail.errMsg : '加载失败') : '未知错误';
      console.log('错误详情:', errMsg);

      // 直接设置错误状态，因为我们不再使用视频元素
      item.videoError = true;
      item.errorMessage = errMsg;

      // 显示错误提示
      uni.showToast({
        title: '视频加载失败: ' + errMsg,
        icon: 'none',
        duration: 2000
      });
    },

    // 不再需要处理列表中的视频播放结束

    // 重试加载视频 - 简化版，只重置错误状态并打开弹窗
    handleRetry(item) {
      console.log('重试加载视频:', item.id);

      // 重置错误状态
      item.videoError = false;
      item.errorMessage = '';

      // 显示加载中提示
      uni.showLoading({
        title: '重新加载中...',
        mask: true
      });

      // 添加时间戳参数来避免缓存
      if (item.video_url) {
        // 获取原始URL（不带时间戳参数）
        let originalUrl = item.video_url;

        // 检查是否是相对路径，如果是则转换为绝对路径
        if (originalUrl.startsWith('/')) {
          originalUrl = config.HTTP_REQUEST_URL + originalUrl;
        }

        // 先清除可能存在的时间戳参数
        if (originalUrl.includes('?')) {
          const urlParts = originalUrl.split('?');
          const baseUrl = urlParts[0];
          const params = new URLSearchParams(urlParts[1]);
          params.delete('_t'); // 删除已有的时间戳参数
          originalUrl = baseUrl + (params.toString() ? '?' + params.toString() : '');
        }

        // 添加新的时间戳参数
        const timestamp = Date.now();
        if (originalUrl.includes('?')) {
          item.video_url = `${originalUrl}&_t=${timestamp}`;
        } else {
          item.video_url = `${originalUrl}?_t=${timestamp}`;
        }

        console.log('更新后的视频URL:', item.video_url);

        // 隐藏加载提示
        uni.hideLoading();

        // 直接打开视频弹窗
        this.openVideoModal(item);
      } else {
        console.error('视频URL为空，无法重试');
        uni.showToast({
          title: '视频地址无效，无法重试',
          icon: 'none',
          duration: 2000
        });
        uni.hideLoading();
      }
    },

    // 打开视频弹窗
    openVideoModal(video) {
      console.log('打开视频弹窗:', video);
      this.currentVideo = video;
      this.showVideoModal = true;
      this.isModalVideoLoading = true;
      this.modalVideoPlaybackStarted = false;
      this.isFullscreenMode = false;

      // 延迟检查全屏状态
      this.fullscreenCheckTimer = setInterval(() => {
        this.checkFullscreenStatus();
      }, 1000);

      // 预加载视频
      this.preloadVideo(video.video_url);
    },

    // 预加载视频
    preloadVideo(url) {
      if (!url) return;

      const videoContext = uni.createVideoContext('modalVideo');
      if (videoContext) {
        videoContext.stop();
        videoContext.src = url;
        videoContext.load();
      }
    },

    // 检查全屏状态
    checkFullscreenStatus() {
      const videoContext = uni.createVideoContext('modalVideo');
      if (videoContext) {
        videoContext.requestFullScreen({
          direction: 90,
          success: () => {
            this.isFullscreenMode = true;
          },
          fail: () => {
            this.isFullscreenMode = false;
          }
        });
      }
    },

    // 视频加载完成
    onVideoLoaded() {
      console.log('视频加载完成');
      this.isModalVideoLoading = false;
      this.startVideoPlayback();
    },

    // 开始视频播放
    startVideoPlayback() {
      if (!this.modalVideoPlaybackStarted) {
        const videoContext = uni.createVideoContext('modalVideo');
        if (videoContext) {
          videoContext.play();
          this.modalVideoPlaybackStarted = true;
        }
      }
    },

    // 视频播放错误
    onVideoError(error) {
      console.error('视频播放错误:', error);
      this.isModalVideoLoading = false;
      uni.showToast({
        title: '视频加载失败，请重试',
        icon: 'none',
        duration: 2000
      });
    },

    // 关闭视频弹窗
    closeVideoModal() {
      console.log('关闭视频弹窗');
      this.showVideoModal = false;
      this.currentVideo = null;
      this.isModalVideoLoading = false;
      this.modalVideoPlaybackStarted = false;
      this.isFullscreenMode = false;

      // 停止视频播放
      const videoContext = uni.createVideoContext('modalVideo');
      if (videoContext) {
        videoContext.stop();
      }

      // 清除全屏检查定时器
      if (this.fullscreenCheckTimer) {
        clearInterval(this.fullscreenCheckTimer);
        this.fullscreenCheckTimer = null;
      }
    },

    // 处理视频播放错误
    handleVideoError(video) {
      console.error('视频播放错误:', video);
      video.videoError = true;
      video.errorMessage = '视频加载失败';
      video.retryCount = (video.retryCount || 0) + 1;

      if (video.retryCount <= 3) {
        setTimeout(() => {
          this.retryVideoPlayback(video);
        }, 2000 * video.retryCount);
      } else {
        uni.showToast({
          title: '视频加载失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 重试视频播放
    retryVideoPlayback(video) {
      console.log('重试视频播放:', video);
      video.videoError = false;
      video.errorMessage = '';
      const videoContext = uni.createVideoContext(`video-${video.id}`);
      if (videoContext) {
        videoContext.stop();
        videoContext.src = video.video_url;
        videoContext.play();
      }
    },

    // 处理视频播放
    handleVideoPlay(video) {
      console.log('视频开始播放:', video);
      video.videoError = false;
      video.errorMessage = '';
      video.retryCount = 0;
    },

    // 处理视频暂停
    handleVideoPause(video) {
      console.log('视频暂停:', video);
    },

    // 处理视频结束
    handleVideoEnded(video) {
      console.log('视频播放结束:', video);
      const videoContext = uni.createVideoContext(`video-${video.id}`);
      if (videoContext) {
        videoContext.seek(0);
        videoContext.play();
      }
    },

    // 清理视频URL，添加时间戳参数
    cleanVideoUrl(url) {
      if (!url) return '';

      try {
        // 检查是否是相对路径，如果是则转换为绝对路径
        if (url.startsWith('/')) {
          url = config.HTTP_REQUEST_URL + url;
        }

        // 清除可能存在的时间戳参数
        let cleanUrl = url;
        if (cleanUrl.includes('?')) {
          const urlParts = cleanUrl.split('?');
          const baseUrl = urlParts[0];
          const params = new URLSearchParams(urlParts[1]);
          params.delete('_t');
          cleanUrl = baseUrl + (params.toString() ? '?' + params.toString() : '');
        }

        // 添加新的时间戳参数
        const timestamp = Date.now();
        if (cleanUrl.includes('?')) {
          cleanUrl = `${cleanUrl}&_t=${timestamp}`;
        } else {
          cleanUrl = `${cleanUrl}?_t=${timestamp}`;
        }

        return cleanUrl;
      } catch (error) {
        console.error('处理视频URL时出错:', error);
        return url;
      }
    },

    // 确认删除视频
    confirmDelete(item) {
      uni.showModal({
        title: '确认删除',
        content: '删除后不可恢复，确定要删除这个视频吗？',
        success: (res) => {
          if (res.confirm) {
            this.deleteVideo(item);
          }
        }
      });
    },

    // 删除视频
    deleteVideo(item) {
      uni.showLoading({
        title: '删除中...'
      });

      uni.request({
        url: `${config.HTTP_REQUEST_URL}/api/videos/${item.id}`,
        method: 'DELETE',
        header: {
          'Authorization': `Bearer ${uni.getStorageSync('accessToken')}`
        },
        success: (res) => {
          if (res.statusCode === 200) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });

            // 从列表中移除该视频
            const index = this.photoList.items.findIndex(video => video.id === item.id);
            if (index !== -1) {
              this.photoList.items.splice(index, 1);
            }
          } else {
            uni.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        },
        fail: (error) => {
          console.error('删除视频失败:', error);
          uni.showToast({
            title: '删除失败',
            icon: 'none'
          });
        },
        complete: () => {
          uni.hideLoading();
        }
      });
    },

    // 生成分享token
    generateShareToken() {
      const timestamp = Date.now();
      const random = Math.random().toString(36).substring(2);
      return `${timestamp}.${random}`;
    },

    // 重新生成视频
    regenerateVideo(item) {
      // 检查用户是否已登录
      if (!this.hasLogin) {
        // 未登录，跳转到登录页面
        this.$mHelper.navigateTo('/pages/user/login/index');
        return;
      }

      // 获取用户余额
      const userInfo = this.userInfo || uni.getStorageSync('userInfo');
      const userMoney = userInfo && userInfo.money && userInfo.money.money ? parseFloat(userInfo.money.money) : 0;
      const cost = 1; // 假设重新生成的成本是1元

      // 检查余额是否足够
      if (userMoney < cost) {
        uni.showToast({
          title: '余额不足，请充值',
          icon: 'none',
          duration: 2000
        });

        // 延迟跳转到充值页面
        setTimeout(() => {
          this.$mHelper.navigateTo('/pages/user/missCoin/index');
        }, 1500);
        return;
      }

      // 跳转到视频生成页面
      uni.navigateTo({
        url: `/pages/videogeneration/index?image=${encodeURIComponent(item.cover_image_url)}&description=${encodeURIComponent(item.prompt || '')}&cost=${cost}`,
        fail: (err) => {
          console.error('跳转失败:', err);
          uni.showToast({
            title: '跳转失败',
            icon: 'none'
          });
        }
      });
    },

    // 处理全屏变化事件
    handleFullscreenChange(event) {
      // 安全检查：确保事件对象存在
      if (!event) {
        console.log('全屏状态变化事件对象为空');
        return;
      }

      console.log('全屏状态变化:', event.detail);

      // 安全地获取全屏状态
      let fullScreen = false;
      try {
        if (event.detail && typeof event.detail.fullScreen !== 'undefined') {
          fullScreen = event.detail.fullScreen;
        } else if (event.target && typeof event.target.fullscreenElement !== 'undefined') {
          fullScreen = !!event.target.fullscreenElement;
        } else {
          // 在H5环境中，尝试使用document对象检测全屏状态
          try {
            if (typeof document !== 'undefined') {
              fullScreen = !!(document.fullscreenElement ||
                            document.webkitFullscreenElement ||
                            document.mozFullScreenElement);
            }
          } catch (docErr) {
            console.log('使用document检测全屏状态失败:', docErr);
          }
        }
      } catch (err) {
        console.error('获取全屏状态失败:', err);
      }

      console.log('全屏状态:', fullScreen);

      // 更新内部全屏状态跟踪
      this.isFullscreenMode = fullScreen;

      // 安全检查：确保currentVideo存在
      if (!this.currentVideo || !this.currentVideo.id) {
        console.log('当前视频对象不存在或ID为空');
        return;
      }

      // 获取视频元素和视频上下文
      const videoId = 'video-player-' + this.currentVideo.id;
      let videoContext = null;

      try {
        videoContext = uni.createVideoContext(videoId, this);
      } catch (ctxErr) {
        console.error('创建视频上下文失败:', ctxErr);
      }

      if (!videoContext) {
        console.log('无法获取视频上下文');
        return;
      }

      // 根据全屏状态调整视频样式
      if (fullScreen) {
        console.log('进入全屏模式');

        // 显示加载提示
        uni.showLoading({
          title: '准备全屏播放...',
          mask: true
        });

        // 隐藏底部按钮区域和关闭按钮
        try {
          // 获取底部按钮区域和关闭按钮元素
          const modalButtons = document.querySelector('.modal-buttons');
          const modalClose = document.querySelector('.modal-close');

          // 隐藏底部按钮区域和关闭按钮
          if (modalButtons) {
            modalButtons.style.display = 'none';
          }

          if (modalClose) {
            modalClose.style.display = 'none';
          }
        } catch (err) {
          console.error('隐藏按钮失败:', err);
        }

        // 延迟一点时间，确保全屏模式已经完全切换
        setTimeout(() => {
          // 隐藏加载提示
          uni.hideLoading();

          try {
            // 简单地播放视频，不做其他复杂操作
            videoContext.play();
          } catch (err) {
            console.error('全屏播放失败:', err);
          }
        }, 500);
      } else {
        console.log('退出全屏模式');

        // 恢复底部按钮区域和关闭按钮的显示
        try {
          // 获取底部按钮区域和关闭按钮元素
          const modalButtons = document.querySelector('.modal-buttons');
          const modalClose = document.querySelector('.modal-close');

          // 恢复底部按钮区域和关闭按钮的显示
          if (modalButtons) {
            modalButtons.style.display = 'flex';
          }

          if (modalClose) {
            modalClose.style.display = 'flex';
          }
        } catch (err) {
          console.error('恢复按钮显示失败:', err);
        }

        // 简单地继续播放视频
        try {
          videoContext.play();
        } catch (err) {
          console.error('退出全屏后播放失败:', err);
        }
      }
    },

    // 处理弹窗视频错误
    handleModalVideoError(e) {
      console.error('弹窗视频加载错误:', e);

      // 隐藏可能存在的加载提示
      uni.hideLoading();

      if (!this.currentVideo) {
        return;
      }

      // 获取错误详情
      const errMsg = e && e.detail ? e.detail.errMsg : '未知错误';
      console.log('错误详情:', errMsg);

      // 检查是否已经重试过
      if (!this.currentVideo.modalRetryCount || this.currentVideo.modalRetryCount < 2) {
        // 增加重试计数
        this.currentVideo.modalRetryCount = (this.currentVideo.modalRetryCount || 0) + 1;
        console.log(`尝试重新加载弹窗视频 (${this.currentVideo.modalRetryCount}/2)...`);

        // 显示错误提示
        uni.showToast({
          title: '视频加载失败，正在重试',
          icon: 'none',
          duration: 2000
        });

        // 尝试刷新视频URL
        if (this.currentVideo.video_url) {
          // 使用原始URL，不做任何处理

          // 延迟一点时间，确保DOM更新后再操作视频
          setTimeout(() => {
            try {
              // 获取视频上下文并重置
              const videoId = 'video-player-' + this.currentVideo.id;
              const videoContext = uni.createVideoContext(videoId, this);
              if (videoContext) {
                videoContext.play(); // 尝试播放
              }
            } catch (err) {
              console.error('重新播放视频失败:', err);
            }
          }, 300);
        }
      } else {
        // 如果重试次数达到上限，显示最终错误提示
        uni.showToast({
          title: '视频无法播放，请稍后再试',
          icon: 'none',
          duration: 2000
        });

        // 延迟关闭弹窗
        setTimeout(() => {
          this.closeVideoModal();
        }, 2000);
      }
    },

    // 处理弹窗视频播放结束
    handleModalVideoEnded() {
      console.log('弹窗视频播放结束');

      if (this.currentVideo) {
        // 重置视频到初始状态，但不关闭弹窗
        const videoId = 'video-player-' + this.currentVideo.id;
        const videoContext = uni.createVideoContext(videoId, this);

        if (videoContext) {
          // 延迟一点时间再重置，避免闪烁
          setTimeout(() => {
            videoContext.stop(); // 停止播放
            videoContext.seek(0); // 重置到开始位置
          }, 100);
        }
      }
    },

    // 处理弹窗视频播放开始
    handleModalVideoPlay() {
      console.log('弹窗视频开始播放');

      // 不对背景音乐做任何操作，保持当前状态
      console.log('视频开始播放，音乐播放状态保持不变');

      // 隐藏加载状态
      this.isModalVideoLoading = false;
    },

    // 处理弹窗视频暂停
    handleModalVideoPause() {
      console.log('弹窗视频暂停');

      // 不对背景音乐做任何操作，保持当前状态
      console.log('视频暂停，音乐播放状态保持不变');
    },

    // 处理视频元数据加载完成
    handleVideoMetadataLoaded(e) {
      console.log('视频元数据加载完成:', e);

      // 视频元数据加载完成，表示视频资源已经准备好
      if (this.isFullscreenMode) {
        console.log('全屏模式下视频元数据加载完成');

        // 在全屏模式下，元数据加载完成后尝试播放视频
        if (this.currentVideo) {
          const videoId = 'video-player-' + this.currentVideo.id;
          const videoContext = uni.createVideoContext(videoId, this);

          if (videoContext) {
            // 尝试播放视频
            setTimeout(() => {
              videoContext.play();
            }, 100);
          }
        }
      }

      // 隐藏加载状态
      this.isModalVideoLoading = false;
    },

    // 处理视频等待加载
    handleVideoWaiting(e) {
      console.log('视频等待加载:', e);

      // 显示加载状态
      this.isModalVideoLoading = true;

      // 如果在全屏模式下等待加载，可能需要特殊处理
      if (this.isFullscreenMode) {
        console.log('全屏模式下视频等待加载');

        // 显示友好的提示
        uni.showToast({
          title: '视频加载中...',
          icon: 'loading',
          duration: 2000
        });
      }
    },

    // 处理视频加载进度
    handleVideoProgress(e) {
      console.log('视频加载进度:', e);

      // 如果视频正在加载，可以在这里更新加载进度
      if (this.isModalVideoLoading && this.isFullscreenMode) {
        // 在全屏模式下，可以显示加载进度
        // 但由于 uni-app 的 video 组件不提供具体的加载进度信息，
        // 所以这里只能简单地显示加载状态
      }
    },

    // 处理页面级分享按钮点击
    handleShare() {
      console.log('处理分享');
      if (!this.currentVideo) {
        uni.showToast({
          title: '请先选择要分享的视频',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 显示加载提示
      uni.showLoading({
        title: '生成分享链接...',
        mask: true
      });

      // 生成分享token
      uni.request({
        url: `${config.HTTP_REQUEST_URL}/api/videos/${this.currentVideo.id}/share-token`,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${uni.getStorageSync('accessToken')}`
        },
        success: (res) => {
          if (res.statusCode === 200 && res.data.token) {
            // 构建分享链接
            const shareUrl = `${config.HTTP_REQUEST_URL}/share/video/${res.data.token}`;
            
            // 复制到剪贴板
            uni.setClipboardData({
              data: shareUrl,
              success: () => {
                uni.showToast({
                  title: '分享链接已复制',
                  icon: 'success',
                  duration: 2000
                });
              },
              fail: (error) => {
                console.error('复制分享链接失败:', error);
                uni.showToast({
                  title: '复制分享链接失败',
                  icon: 'none',
                  duration: 2000
                });
              }
            });
          } else {
            this.handleShareError('生成分享链接失败');
          }
        },
        fail: (error) => {
          this.handleShareError('生成分享链接请求失败', error);
        },
        complete: () => {
          uni.hideLoading();
        }
      });
    },

    // 处理分享错误
    handleShareError(message, error = null) {
      console.error(message, error);
      uni.showToast({
        title: message,
        icon: 'none',
        duration: 2000
      });
    },

    // 处理分享到微信
    handleShareToWechat() {
      if (!this.currentVideo) {
        uni.showToast({
          title: '请先选择要分享的视频',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 显示加载提示
      uni.showLoading({
        title: '准备分享...',
        mask: true
      });

      // 生成分享token
      uni.request({
        url: `${config.HTTP_REQUEST_URL}/api/videos/${this.currentVideo.id}/share-token`,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${uni.getStorageSync('accessToken')}`
        },
        success: (res) => {
          if (res.statusCode === 200 && res.data.token) {
            // 构建分享链接
            const shareUrl = `${config.HTTP_REQUEST_URL}/share/video/${res.data.token}`;
            
            // 调用微信分享
            uni.share({
              provider: 'weixin',
              scene: 'WXSceneSession',
              type: 0,
              title: this.currentVideo.title || '分享视频',
              summary: this.currentVideo.description || '快来看看这个精彩的视频',
              imageUrl: this.currentVideo.cover_image_url,
              href: shareUrl,
              success: () => {
                uni.showToast({
                  title: '分享成功',
                  icon: 'success',
                  duration: 2000
                });
              },
              fail: (error) => {
                console.error('微信分享失败:', error);
                uni.showToast({
                  title: '分享失败，请重试',
                  icon: 'none',
                  duration: 2000
                });
              }
            });
          } else {
            this.handleShareError('生成分享链接失败');
          }
        },
        fail: (error) => {
          this.handleShareError('生成分享链接请求失败', error);
        },
        complete: () => {
          uni.hideLoading();
        }
      });
    },

    // 处理弹窗中的分享按钮点击
    handleModalShare() {
      console.log('点击弹窗分享按钮');

      if (!this.currentVideo) {
        console.error('当前视频对象为空，无法分享');
        return;
      }

      const videoId = this.currentVideo.id;

      // 生成分享token并保存
      const shareToken = this.generateShareToken();
      // 设置过期时间为3天
      const expireTime = Date.now() + (3 * 24 * 60 * 60 * 1000);
      uni.setStorageSync(`share_token_${videoId}`, { token: shareToken, expireTime });

      // 构建分享链接
      const shareUrl = location.origin + '/#' + `/pages/share/index?video_id=${videoId}&token=${shareToken}`;

      // 复制链接到剪贴板
      uni.setClipboardData({
        data: shareUrl,
        success: () => {
          // 显示复制成功提示
          uni.showToast({
            title: '链接已复制，可分享给好友',
            icon: 'none',
            duration: 2000
          });
        },
        fail: (err) => {
          console.error('复制链接失败:', err);

          // 如果复制失败，显示链接，让用户手动复制
          uni.showModal({
            title: '复制链接失败',
            content: '请手动复制以下链接：\n' + shareUrl,
            showCancel: false,
            confirmText: '知道了'
          });
        }
      });
    },

    // 生成随机分享token
    generateShareToken() {
      return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    },

    // 处理全屏模式下的分享按钮点击
    handleFullscreenShare() {
      console.log('点击全屏分享按钮');

      // 确保当前视频存在
      if (!this.currentVideo) {
        console.error('当前视频对象为空，无法分享');
        return;
      }

      const videoId = this.currentVideo.id;

      // 生成分享token并保存
      const shareToken = this.generateShareToken();
      // 设置过期时间为3天
      const expireTime = Date.now() + (3 * 24 * 60 * 60 * 1000);
      uni.setStorageSync(`share_token_${videoId}`, { token: shareToken, expireTime });

      // 构建分享链接 - 使用 history 模式
      const shareUrl = location.origin + `/pages/share/index?video_id=${videoId}&token=${shareToken}`;

      // 复制链接到剪贴板
      uni.setClipboardData({
        data: shareUrl,
        success: () => {
          // 显示复制成功提示
          uni.showToast({
            title: '链接已复制，可分享给好友',
            icon: 'none',
            duration: 2000
          });
        },
        fail: (err) => {
          console.error('复制链接失败:', err);

          // 如果复制失败，显示链接，让用户手动复制
          uni.showModal({
            title: '复制链接失败',
            content: '请手动复制以下链接：\n' + shareUrl,
            showCancel: false,
            confirmText: '知道了'
          });
        }
      });
    },
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  background-color: #F8FBFF;
  min-height: 100vh;
  padding-bottom: 120rpx; /* 增加底部内边距，确保内容不被底部导航栏遮挡 */
  box-sizing: border-box; /* 确保内边距计算在盒子模型内 */
}

.button-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #FFFFFF;
  width: 96%;
  max-width: 740rpx;
  margin: 20rpx auto 10rpx; /* 减少底部间距 */
  border-radius: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border-left: 4rpx solid #CB785D;
  box-sizing: border-box;
}

.music-selector {
  display: flex;
  align-items: center;
  gap: 10rpx;
  flex: 1;
}

.music-picker {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: rgba(255, 107, 0, 0.1);
  border-radius: 8rpx;
  padding: 10rpx 20rpx;
  border: 1px solid rgba(255, 107, 0, 0.2);
}

.music-label {
  font-size: 28rpx;
  color: #ff9500;
}

.music-value {
  font-size: 28rpx;
  color: #333333;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.music-control-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: linear-gradient(135deg, #ff6b00, #ff9500);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin-left: 10rpx;
}

.music-control-icon {
  color: #FFFFFF;
  font-size: 32rpx;
  line-height: 1;
}

.button-group {
  display: flex;
  align-items: center;
}

.refresh-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin-right: 10rpx;
}

.refresh-icon {
  color: #FFFFFF;
  font-size: 32rpx;
  line-height: 1;
}

.share-btn {
  background-color: #CB785D;
  color: #FFFFFF;
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
}

.album-list {
  width: 96%;
  max-width: 740rpx;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 设置两列布局 */
  gap: 20rpx; /* 设置网格间距 */
  background-color: #F8FBFF; /* 确保与页面背景色一致 */
  padding-top: 10rpx; /* 添加顶部内边距，减少与button-container的间距 */
}

.album-item {
  width: 100%;
  background-color: #FFFFFF;
  border-radius: 10rpx;
  overflow: hidden; /* 确保内容不溢出 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.photo-container {
  width: 100%;
  height: 320rpx; /* 调整高度适合两列布局 */
  border-radius: 10rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.photo-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.play-button-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.2);
}

.play-button {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
}

.play-icon {
  color: #CB785D;
  font-size: 40rpx;
  margin-left: 6rpx; /* 稍微调整一下位置，使其看起来居中 */
}

.processing-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
}

.processing-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  padding: 10rpx;
  text-align: center;
}

.processing-text {
  font-size: 24rpx; /* 减小字体大小 */
  margin-bottom: 6rpx; /* 减小间距 */
}

.spinner-container {
  margin: 10rpx 0; /* 减小间距 */
}

.spinner {
  width: 50rpx; /* 减小尺寸 */
  height: 50rpx; /* 减小尺寸 */
  border: 3rpx solid rgba(255, 255, 255, 0.3); /* 减小边框 */
  border-top: 3rpx solid #FFFFFF; /* 减小边框 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-status {
  font-size: 28rpx; /* 减小字体大小 */
  font-weight: bold;
  margin-top: 6rpx; /* 减小间距 */
}

.dots {
  animation: dots 1.5s infinite;
}

@keyframes dots {
  0%, 20% { content: '.'; }
  40% { content: '..'; }
  60%, 100% { content: '...'; }
}

.error-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.7);
  color: #FFFFFF;
  padding: 20rpx;
  box-sizing: border-box;
}

.error-text {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.error-detail {
  font-size: 24rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.retry-btn, .regenerate-btn {
  background-color: rgba(255, 107, 0, 0.8);
  color: #FFFFFF;
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  margin: 10rpx;
}

.video-delete-btn {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  z-index: 10;
}

.delete-icon {
  color: #FFFFFF;
  font-size: 40rpx;
  line-height: 1;
}

.empty-state {
  width: 96%;
  max-width: 740rpx;
  height: 400rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999999;
  margin: 0 auto;
  background-color: #F8FBFF; /* 确保与页面背景色一致 */
  grid-column: 1 / -1; /* 空状态占据所有列 */
}

.empty-text {
  font-size: 32rpx;
}

/* 视频弹窗样式 */
.video-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  width: 100%;
  height: 100%;
  background-color: #000;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  padding-bottom: 150rpx; /* 减少底部填充，适应按钮区域的新位置 */
  padding-top: 20rpx; /* 添加顶部填充 */
}

.modal-video-container {
  flex: 1;
  width: 100%;
  height: 100%;
  background-color: #000;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden; /* 防止内容溢出 */
  max-height: calc(100% - 100rpx); /* 限制最大高度，留出更多空间给按钮 */
  padding: 0; /* 移除内边距 */
  margin: 0; /* 移除外边距 */
}

/* 视频加载指示器样式 */
.video-loading-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #FFFFFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  color: #FFFFFF;
  font-size: 28rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

.modal-video {
  width: 100%; /* 设置宽度为100%，填满容器 */
  height: 100%; /* 设置高度为100%，填满容器 */
  object-fit: contain; /* 保持视频比例，确保完整显示 */
  position: absolute; /* 使用绝对定位，确保完全覆盖容器 */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto; /* 居中显示 */
  display: block;
  background-color: #000; /* 保持黑色背景 */
}

/* 为9:16的竖屏视频添加特殊样式 */
.portrait-video {
  width: auto; /* 自动计算宽度 */
  height: 85%; /* 增加高度百分比，使竖屏视频显示更大 */
  max-width: 70%; /* 限制最大宽度，防止宽度过大 */
}

/* 全屏状态下的视频样式 */
.fullscreen-video {
  width: 100% !important;
  height: 100% !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  max-height: none !important;
  z-index: 9999 !important;
}

/* 全屏分享按钮样式 */
.fullscreen-share-btn {
  position: absolute;
  top: 30rpx;
  left: 30rpx;
  z-index: 10000;
  background: linear-gradient(to right, #00c6fb, #005bea);
  color: white;
  padding: 15rpx 30rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.5);
}

.fullscreen-share-icon {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.modal-buttons {
  display: flex;
  padding: 20rpx 30rpx;
  justify-content: space-around;
  background-color: rgba(0, 0, 0, 0.7);
  position: fixed; /* 改为 fixed 以确保始终显示在底部 */
  bottom: 60px; /* 向上移动40px */
  left: 0;
  right: 0;
  z-index: 1000; /* 增加 z-index 确保始终显示在最上层 */
  border-top: 2rpx solid rgba(255, 255, 255, 0.1); /* 添加上边框，增强与视频的分隔感 */
  border-radius: 20rpx 20rpx 0 0; /* 添加上边框圆角 */
  box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.3); /* 添加阴影，增强分隔感 */
}

.modal-btn {
  width: 45%;
  height: 90rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background-color: transparent;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
  margin: 10rpx 0; /* 添加上下边距 */
}

.modal-btn.share-btn {
  background: linear-gradient(to right, #00c6fb, #005bea);
}

.modal-btn.regenerate-btn {
  background: linear-gradient(to right, #ff9a9e, #ff5757);
}

.btn-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.modal-close {
  position: fixed; /* 改为 fixed 以确保始终显示 */
  top: 40rpx;
  right: 40rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001; /* 增加 z-index 确保始终显示在最上层 */
}

.close-icon {
  color: #FFFFFF;
  font-size: 40rpx;
  line-height: 1;
}

.music-tip-content {
  width: 80%;
  max-width: 600rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  text-align: center;
}

.tip-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.tip-text {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.tip-button {
  background: linear-gradient(135deg, #ff6b00, #ff9500);
  color: #FFFFFF;
  font-size: 32rpx;
  padding: 20rpx 60rpx;
  border-radius: 40rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 0, 0.3);
}
</style>
