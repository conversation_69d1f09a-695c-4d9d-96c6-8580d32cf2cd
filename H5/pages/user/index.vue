<template>
	<view class="user center_column">
		<view class="user_header">
			<view class="user_info flex align_items_center" @tap="$mHelper.loginNavigateTo('/pages/user/info/set_up')">
				<block v-if="hasLogin">
					<image class="user_info_header" :src="userInfo.avatar?userInfo.avatar+'?imageView2/3/w/200':'/static/user/my_user_no.png'" mode=""></image>
					<view class="user_info_content">
						<view class="name flex align_items_center">
							<text class="fontSize18 color1 fontWeightBold">{{userInfo.name}}</text>
							<image src="/static/user/my_icon_edit.png" mode=""></image>
						</view>
						<view class="info_wallet flex align_items_center">
							<view class="fontSize12">思念币:{{userInfo.money.money}}</view>
							<view class="fontSize12">云积分:{{userInfo.money.point}}</view>
						</view>
					</view>
				</block>
				<block v-else>
					<image class="user_info_header" src="/static/user/my_user_no.png" mode=""></image>
					<view class="user_info_no color1 fontSize18 fontWeightBold">点击登录</view>
				</block>
				<view class="user_info_sign top0 flex align_items_center" @tap.stop="signIn">
					<image src="/static/user/my_icon_qiandao.png" mode=""></image>
					<text class="colorCB785D fontSize14">签到</text>
				</view>
				<view class="user_info_sign top90 flex align_items_center" @tap.stop="$mHelper.loginNavigateTo('/pages/user/missCoin/index')">
					<image src="/static/user/my_icon_cz.png" mode=""></image>
					<text class="colorfff fontSize14">充值</text>
				</view>
			</view>
		</view>
		<view class="user_tab">
			<view class="user_tab_title">
				<text class="color1 fontSize16 fontWeightBold">我的服务</text>
			</view>
			<view class="user_tab_box marginBottom34 center_between">
				<view class="user_tab_content center_column" @tap="$mHelper.loginNavigateTo('/pages/user/administer/index')">
					<image src="/static/user/my_icon_jng.png" mode=""></image>
					<text class="color1 fontSize14">纪念馆管理</text>
				</view>
				<!-- <view class="user_tab_content center_column" @tap="$mHelper.loginNavigateTo('/pages/user/share/rank')">
					<image src="/static/user/my_icon_js.png" mode=""></image>
					<text class="color1 fontSize14">祭祀道具</text>
				</view> -->
				<view class="user_tab_content center_column" @tap="$mHelper.loginNavigateTo('/pages/user/missCoin/index')">
					<image src="/static/user/my_icon_snb.png" mode=""></image>
					<text class="color1 fontSize14">思念币</text>
				</view>
				<view class="user_tab_content center_column" @tap="$mHelper.loginNavigateTo('/pages/user/cloudIntegral/index')">
					<image src="/static/user/my_icon_yjf.png" mode=""></image>
					<text class="color1 fontSize14">云积分</text>
				</view>
			</view>
			<view class="user_tab_box center_between">
				<!-- <view class="user_tab_content center_column" @tap="$mHelper.loginNavigateTo('/pages/user/collect/index')">
					<image src="/static/user/my_icon_sc.png" mode=""></image>
					<text class="color1 fontSize14">我的收藏</text>
				</view> -->
				<view class="user_tab_content center_column" @tap="$mHelper.loginNavigateTo('/pages/obituary/info/obituary')">
					<image src="/static/user/my_icon_yfg.png" mode=""></image>
					<text class="color1 fontSize14">我的云讣告</text>
				</view>
				<view class="user_tab_content center_column" @tap="$mHelper.loginNavigateTo('/pages/user/order/list')">
					<image src="/static/user/my_icon_dd.png" mode=""></image>
					<text class="color1 fontSize14">我的订单</text>
				</view>
				<!-- <view class="user_tab_content center_column" @tap="$mHelper.loginNavigateTo('/pages/space/record/index')">
					<image src="/static/user/my_icon_gfjl.png" mode=""></image>
					<text class="color1 fontSize14">供奉记录</text>
				</view> -->
				<view class="user_tab_content center_column" @tap="$mHelper.loginNavigateTo('/pages/user/rank/index')">
					<image src="/static/user/my_icon_phb.png" mode=""></image>
					<text class="color1 fontSize14">排行榜</text>
				</view>
			</view>
		</view>
		<view class="user_tab" style="margin-top: 24rpx;">
			<view class="user_tab_title">
				<text class="color1 fontSize16 fontWeightBold">照片处理</text>
			</view>
			<view class="user_tab_box center_between">
				<view class="user_tab_content center_column" @tap="$mHelper.loginNavigateTo(`/pages/user/info/albumHandle?key=zhaopianxiufu&title=照片修复`)">
					<image src="/static/user/my_icon_zpxf.png" mode=""></image>
					<text class="color1 fontSize14">照片修复</text>
				</view>
				<view class="user_tab_content center_column" @tap="$mHelper.loginNavigateTo(`/pages/user/info/albumHandle?key=suiyuezhaopian&title=岁月照片`)">
					<image src="/static/user/my_icon_syzp.png" mode=""></image>
					<text class="color1 fontSize14">岁月照片</text>
				</view>
				<view class="user_tab_content center_column" @tap="$mHelper.loginNavigateTo(`/pages/user/info/albumHandle?key=dongtaizhaopian&title=动态照片`)">
					<image src="/static/user/my_icon_dtzp.png" mode=""></image>
					<text class="color1 fontSize14">动态照片</text>
				</view>
			</view>
		</view>

		<!-- 新增追忆空间区域 -->
		<view class="user_tab" style="margin-top: 24rpx;">
			<view class="user_tab_title">
				<text class="color1 fontSize16 fontWeightBold">追忆服务</text>
			</view>
			<view class="user_tab_box center_between">
				<view class="user_tab_content center_column" @tap="$mHelper.switchTab('/pages/home/<USER>')">
					<image src="/static/user/think.png" mode=""></image>
					<text class="color1 fontSize14">云忆Meta</text>
				</view>
				<view class="user_tab_content center_column" @tap="$mHelper.switchTab('/pages/establish/index')">
					<image src="/static/user/my_icon_jng.png" mode=""></image>
					<text class="color1 fontSize14">创建纪念堂</text>
				</view>
				<view class="user_tab_content center_column" @tap="$mHelper.switchTab('/pages/space/index')">
					<image src="/static/tab/space_select.png" mode=""></image>
					<text class="color1 fontSize14">追忆空间</text>
				</view>
			</view>
		</view>
		<view class="user_list">
			<!-- <view class="user_list_box flex align_items_center" @tap="$mHelper.loginNavigateTo('/pages/user/commission/index')">
				<image class="user_list_box_image" src="/static/user/my_icon_tg.png" mode=""></image>
				<view class="user_list_box_content center_between">
					<text class="color1 fontSize14">代理推广</text>
					<image src="/static/public/icon_back.png" mode=""></image>
				</view>
			</view> -->
			<view class="user_list_box flex align_items_center" @tap="$mHelper.loginNavigateTo('/pages/common/info?key=contact_us&title=联系客服')">
				<image class="user_list_box_image" src="/static/user/my_icon_kf.png" mode=""></image>
				<view class="user_list_box_content center_between">
					<text class="color1 fontSize14">联系客服</text>
					<image src="/static/public/icon_back.png" mode=""></image>
				</view>
			</view>
			<view class="user_list_box flex align_items_center" @tap="$mHelper.loginNavigateTo('/pages/user/info/set_up')">
				<image class="user_list_box_image" src="/static/user/my_icon_sz.png" mode=""></image>
				<view class="user_list_box_content center_between">
					<text class="color1 fontSize14">设置</text>
					<image src="/static/public/icon_back.png" mode=""></image>
				</view>
			</view>
		</view>
		<new-footer-menu :active="'user'" />
		<my-sign-in ref="signIn" :signinData="signinData"></my-sign-in>
	</view>
</template>

<script>
	import { getUserInfo, signin } from '@/api/user';
	export default{
		data(){
			return{
				hasLogin:false,
				signinData:{},
				userInfo:uni.getStorageSync('userInfo') || {}
			}
		},
		onShow() {
			this.hasLogin = this.$mStore.getters.hasLogin;
			if(this.hasLogin){
				getApp().globalData.getUserInfo();
				this.userInfo = uni.getStorageSync('userInfo');
			}
		},
		onReady() {

		},
		methods:{
			// 签到
			signIn(){
				if(this.hasLogin){
					this.$mHelper.showLoading('签到中...');
					signin().then(async r => {
						this.$mHelper.hideLoading();
						this.signinData = r.data;
						this.$refs.signIn.showSignIn = true;
					}).catch(e => {
						this.$mHelper.hideLoading();
						this.$mHelper.toast(e.msg);
					});
				}else{
					this.$mHelper.navigateTo('/pages/user/login/index')
				}
			}
		}
	}
</script>

<style lang="scss" scoped >
	.user{
		width: 100%;
		overflow-x: hidden;
		.user_header{
			width: 100%;
			height: 588rpx;
			background-size: 100% 100%;
			background-image: url('https://yjs-1312427740.cos.ap-beijing.myqcloud.com/fontend/my_bg_top.png');
			.user_info{
				width: 690rpx;
				position: relative;
				margin-top: 110rpx;
				padding-top: var(--status-bar-height);
				.user_info_header{
					width: 152rpx;
					height: 152rpx;
					border-radius: 50%;
					margin-right: 20rpx;
					background-color: #fff;
					margin-left: 44rpx;
				}
				.user_info_no{
					height: 152rpx;
					line-height: 152rpx;
					margin-left: 22rpx;
				}
				.user_info_content{
					.name{
						margin-bottom: 12rpx;
						text{
							margin-right: 6rpx;
						}
						image{
							width: 36rpx;
							height: 34rpx;
						}
					}
					.info_wallet{
						view{
							padding: 8rpx;
							border-radius: 8rpx;
							&:first-child{
								color: #CB785D;
								background: rgba(203, 120, 93, .21);
								margin-right: 20rpx;
							}
							&:last-child{
								color: #0091FF;
								background: rgba(0, 145, 255, .21);
							}
						}
					}
				}
				.top0{
					top: 0rpx;
				}
				.top90{
					top: 90rpx;
					background-color: #CB785D;
				}
				.user_info_sign{
					width: 256rpx;
					height: 60rpx;
					border-radius: 30rpx;
					position: absolute;
					right: -186rpx;
					border: 1px solid #CB785D;
					image{
						width: 34rpx;
						height: 34rpx;
						margin-left: 20rpx;
					}
					text{
						margin-left: 4rpx;
					}
				}
			}
		}
		.user_tab{
			width: 690rpx;
			padding: 36rpx 0rpx;
			background: #FFFFFF;
			border-radius: 8px;
			margin-top: -250rpx;
			.user_tab_title {
				padding: 0rpx 34rpx 20rpx;
				text {
					position: relative;
					&::after {
						content: '';
						position: absolute;
						left: 0;
						bottom: -10rpx;
						width: 40rpx;
						height: 4rpx;
						background-color: #CB785D;
						border-radius: 2rpx;
					}
				}
			}
			.user_tab_box{
				padding: 0rpx 34rpx;
				.user_tab_content{
					width: 154rpx;
					image{
						width: 56rpx;
						height: 56rpx;
						margin-bottom: 8rpx;
					}
				}
			}
		}
		.user_list{
			width: 690rpx;
			padding: 10rpx 0rpx 30rpx;
			margin-top: 24rpx;
			border-radius: 16rpx;
			background-color: #fff;
			.user_list_box{
				padding: 0rpx 24rpx;
				height: 110rpx;
				.user_list_box_image{
					width: 56rpx;
					height: 56rpx;
					margin-right: 12rpx;
				}
				.user_list_box_content{
					width: 560rpx;
					height: 110rpx;
					border-bottom: 2rpx solid #F0F0F0;
					image{
						width: 36rpx;
						height: 36rpx;
						margin-right: -12rpx;
					}
				}
			}
		}
	}

</style>