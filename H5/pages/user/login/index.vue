<template>
	<view class="login center_column">
		<view class="login_bg">
			<image src="/static/user/login_bg.png" mode=""></image>
		</view>
		<my-back></my-back>
		<view class="login_logo center">
			<image src="/static/user/login_logo.png" mode=""></image>
		</view>
		<view class="login_box center_column">
			<view class="login_box_phone flex align_items_center">
				<image src="/static/user/my_login_phone.png" mode=""></image>
				<input class="fontSize16 color1" v-model="loginParams.mobile" type="text" maxlength="11" placeholder="请输入手机号码" placeholder-style="color:#999999;">
			</view>
			<view class="login_box_code flex align_items_center justify_content_space_between">
				<view class="code_box flex align_items_center">
					<image src="/static/user/my_login_code.png" mode=""></image>
					<input class="fontSize16 color1" v-model="loginParams.code" type="text" maxlength="6" placeholder="请输入验证码" placeholder-style="color:#999999;">
				</view>
				<text class="fontSize16 color1" v-if="!smsCodeBtnDisabled" @tap="getSmsCode">获取验证码</text>
				<text class="fontSize16 colorCB785D" v-else>{{ `重新发送 (${codeSeconds})` }}</text>
			</view>
			
			<view class="login_box_btn btn_bg_color colorfff fontSize18" v-if="loginParams.mobile&&loginParams.code" @tap="toLogin">登录</view>
			<view class="login_box_btn btn_opacity_bg_color colorfff fontSize18" v-else>登录</view>
			
			<view class="login_box_agreement color555 fontSize12 flex align_items_center" @tap="loginParams.protocol=!loginParams.protocol">
				<image v-if="loginParams.protocol" src="/static/user/my_login_icon_choose.png" mode=""></image>
				<image v-if="!loginParams.protocol" src="/static/user/my_login_icon_nochoose.png" mode=""></image>
				我已阅读并同意
				<text class="color4884FF" @tap.stop="$mHelper.navigateTo('/pages/common/info?key=protocol_service&title=用户协议')">《用户协议》</text>
				<text class="color4884FF" @tap.stop="$mHelper.navigateTo('/pages/common/info?key=protocol_privacy&title=隐私政策')">《隐私政策》</text>
			</view>
		</view>
		<view class="login_other center_column" v-if="showWX">
			<view class="login_other_title center_between">
				<view></view>
				<text class="fontSize14 color7A7E88">其他方式登录</text>
				<view></view>
			</view>
			<view class="login_other_image flex align_items_center justify_content_space_around">
				<view>
					<button type='primary' open-type="getUserInfo" lang="zh_CN" @click="wxLogin"></button>
					<image src="/static/user/user_login_wx.png" mode=""></image>
				</view>
			</view>
		</view>
		<my-slidecode v-if="slideCodeShow" @close="slideCodeShow = false" @success="slideCodeSuccess"></my-slidecode>
	</view>
</template>

<script>
	import {
		loginBySmsCodeApi,
		loginByPassworsApi,
		loginByAutheApi,
		smsCodeApi,
		loginByWX
		// getSetting
	} from '@/api/user';
	import {wx_config} from '@/api/home.js'
	import config from '@/config/index.js';
	import moment from '@/common/moment';
	export default {
		data() {
			return {
				showWX:false,
				checkSlideCode: getApp().globalData.userPublicConfig ? getApp().globalData.userPublicConfig.login_slide_code_check : false, // 是否开启图形验证码
				slideCodeShow: false, //图形验证码是否显示
				slideCodeValidated: false, //图形验证码是否验证通过
				slideCodeX: 10, //图形验证码的水平偏移值
				clientId: '',
				loginParams: {
					mobile: '',
					code: '',
					protocol:false
				},
				reqBody: {},
				codeSeconds: 0, // 验证码发送时间间隔
				loginByPass: false,
				smsCodeBtnDisabled: false,
				inviter_id: ''
			};
		},
		onLoad(options) {
			if(this.$mHelper.isWX()&&this.$mHelper.appClientType()!='toutiao'){
				this.showWX = true;
			}else if(this.$mHelper.appClientType()!='h5'&&this.$mHelper.appClientType()!='toutiao'){
				this.showWX = true;
			}
			// this.getSetting();
			if (options.inviter_id) {
				this.inviter_id = options.inviter_id;
			}
			if(options.code){
				this.getWXUserInfo(options.code)
			}
			const time = moment().valueOf() / 1000 - uni.getStorageSync('loginSmsCodeTime');
			if (time < 60) {
				this.codeSeconds = this.$mConstDataConfig.sendCodeTime - parseInt(time, 10);
				this.handleSmsCodeTime(this.codeSeconds);
			} else {
				this.codeSeconds = this.$mConstDataConfig.sendCodeTime;
				this.smsCodeBtnDisabled = false;
				uni.removeStorageSync('loginSmsCodeTime');
			}
		},
		onHide() {
			uni.removeStorageSync('loginSmsCodeTime');
		},
		onUnload() {
			uni.removeStorageSync('loginSmsCodeTime');
		},
		options: {
			styleIsolation: 'shared'
		},
		methods: {
			// 滑动验证码
			slideCodeSuccess: function(obj) {
				//验证通过了
				this.slideCodeValidated = true; //验证通过
				this.slideCodeX = obj.x; //偏移值
				this.clientId = obj.client_id;
				this.getSmsCode(); //获取验证码
			},
			// 发送验证码并进入倒计时
			getSmsCode() {
				this.reqBody = {};
				this.reqBody['mobile'] = this.loginParams['mobile'];
				let checkSendCode = this.$mGraceChecker.check(this.reqBody, this.$mFormRule.sendCodeRule);
				if (!checkSendCode) {
					this.$mHelper.toast(this.$mGraceChecker.error);
					return;
				}
				if (this.checkSlideCode && !this.slideCodeValidated) {
					this.slideCodeShow = true;
					return;
				}
				this.$mHelper.showLoading('发送中...');
				smsCodeApi({
					mobile: this.loginParams.mobile,
					type: 'login',
					x: this.slideCodeX,
					client_id: this.clientId,
				}).then(r => {
					this.$mHelper.hideLoading();
					this.$mHelper.toast(r.message);
					this.smsCodeBtnDisabled = true;
					this.slideCodeValidated = false;
					uni.setStorageSync('loginSmsCodeTime', moment().valueOf() / 1000);
					this.handleSmsCodeTime(59);
				}).catch(e => {
					this.$mHelper.hideLoading();
					this.$mHelper.toast('验证码发送失败，请稍后再试！');
				});
			},
			// 获取验证码发送倒计时
			handleSmsCodeTime(time) {
				let timer = setInterval(() => {
					if (time === 0) {
						clearInterval(timer);
						this.smsCodeBtnDisabled = false;
					} else {
						this.codeSeconds = time;
						this.smsCodeBtnDisabled = true;
						time--;
					}
				}, 1000);
			},
			// 提交表单
			toLogin() {
				this.reqBody = {};
				this.reqBody = this.loginParams;
				this.reqBody['loginType'] = 'mobile';
				let cheRes = this.$mGraceChecker.check(this.reqBody, this.$mFormRule.loginByCodeRule);
				if (!cheRes) {
					this.$mHelper.toast(this.$mGraceChecker.error);
					return;
				}
				this.$mHelper.showLoading('登录中...');
				this.handleLogin(this.reqBody);
			},
			// 登录
			handleLogin(params) {
				loginBySmsCodeApi(params).then(async r => {
					this.$mHelper.hideLoading();
					this.$mHelper.toast('恭喜您，登录成功！');
					this.$mStore.commit('login', r.data);
					uni.$emit('refreshData');
					uni.removeStorageSync('loginSmsCodeTime');
					
					// 获取当前页面栈
					const pages = getCurrentPages();
					if (pages.length > 1) {
						// 如果有上一页，返回上一页
						this.$mHelper.navigateBack();
					} else {
						// 如果没有上一页，跳转到首页
						this.$mHelper.navigateTo('/pages/index/index');
					}
				}).catch(e => {
					this.$mHelper.hideLoading();
					this.$mHelper.toast(e);
				});
			},
			// 微信登录
			wxLogin(){
				this.$mHelper.showLoading('登录中...');
				if(this.$mHelper.isWX()){
					this.wx_config();
					return;
				}
				const _this = this;
				uni.getUserProfile({
					desc: '登录后可同步数据',
					lang: 'zh_CN',
					success: function (getRes) {
						uni.login({
							provider: 'weixin',
							success: function (loginRes) {
								console.log(loginRes)
								config.HEADER['code'] = loginRes.code;
								loginByWX({
									device:'mini_apps',
									code:loginRes.code
								}).then(async r => {
									_this.$mHelper.hideLoading();
									_this.$mHelper.toast('恭喜您，登录成功！');
									_this.$mStore.commit('login', r.data);
									uni.$emit('refreshData');
									uni.removeStorageSync('loginSmsCodeTime');
									
									// 获取当前页面栈
									const pages = getCurrentPages();
									if (pages.length > 1) {
										// 如果有上一页，返回上一页
										_this.$mHelper.navigateBack();
									} else {
										// 如果没有上一页，跳转到首页
										_this.$mHelper.navigateTo('/pages/index/index');
									}
								}).catch(e => {
									_this.$mHelper.hideLoading();
									_this.$mHelper.toast(e);
								});
							},
							fail(e) {
								_this.$mHelper.hideLoading();
								_this.$mHelper.toast(e);
							}
						});
					},
					fail(e) {
						_this.$mHelper.hideLoading();
						_this.$mHelper.toast(e);
					}
				})
			},
			// 微信内置浏览器登录
			wx_config(){
				wx_config({
					url: this.$mHelper.getCurrentPageUrl()
				}).then(res => {
					this.getWXUserInfo()
					this.$mHelper.hideLoading();
					window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${res.data.appId}
					&redirect_uri=${encodeURIComponent(config.HTTP_HTML_URL+'pages/user/login/index')}
					&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect`
				}).catch(e => {
					this.$mHelper.hideLoading();
					this.$mHelper.toast(e);
				});
			},
			// 微信内置浏览器登录
			getWXUserInfo(code){
				this.$mHelper.showLoading('登录中...');
				loginByWX({
					device:'h5',
					code:code
				}).then(async r => {
					this.$mHelper.hideLoading();
					this.$mHelper.toast('恭喜您，登录成功！');
					this.$mStore.commit('login', r.data);
					uni.$emit('refreshData');
					uni.removeStorageSync('loginSmsCodeTime');
					
					// 获取当前页面栈
					const pages = getCurrentPages();
					if (pages.length > 1) {
						// 如果有上一页，返回上一页
						this.$mHelper.navigateBack();
					} else {
						// 如果没有上一页，跳转到首页
						this.$mHelper.navigateTo('/pages/index/index');
					}
				}).catch(e => {
					this.$mHelper.hideLoading();
					this.$mHelper.toast(e);
				});
			}
		}
	};
</script>


<style scoped lang="scss">
	.login{
		width: 100%;
		position: relative;
		.login_bg{
			position: absolute;
			top:102rpx;
			z-index: 1;
			left: 0;
			margin-top: var(--status-bar-height);
			image{
				width: 750rpx;
				height: 1042rpx;
			}
		}
		.login_logo{
			padding-top: var(--status-bar-height);
			margin-top: 150rpx;
			position: relative;
			z-index: 2;
			image{
				width: 384rpx;
				height: 90rpx;
			}
		}
		.login_box{
			width: 690rpx;
			margin-top: 80rpx;
			padding: 64rpx 0rpx 34rpx;
			background: #FFFFFF;
			border-radius: 10px;
			box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.1);
			position: relative;
			z-index: 2;
			.login_box_phone{
				width: 630rpx;
				height: 96rpx;
				border-radius: 4px;
				background: #F6F6F6;
				image{
					width: 44rpx;
					height: 44rpx;
					margin-left: 30rpx;
					margin-right: 30rpx;
				}
				input{
					width: 500rpx;
					height: 96rpx;
					line-height: 96rpx;
				}
			}
			.login_box_code{
				width: 630rpx;
				height: 96rpx;
				margin-top: 40rpx;
				.code_box{
					width: 400rpx;
					height: 96rpx;
					border-radius: 4px;
					background: #F6F6F6;
					image{
						width: 44rpx;
						height: 44rpx;
						margin-left: 30rpx;
						margin-right: 30rpx;
					}
					input{
						width: 260rpx;
						height: 96rpx;
						line-height: 96rpx;
					}
				}
				text{
					width: 200rpx;
					height: 96rpx;
					text-align: right;
					line-height: 96rpx;
				}
			}
			.login_box_btn{
				width: 630rpx;
				height: 88rpx;
				line-height: 88rpx;
				text-align: center;
				margin-top: 68rpx;
				border-radius: 22px;
			}
			.login_box_agreement{
				width: 630rpx;
				margin-top: 40rpx;
				image{
					width: 28rpx;
					height: 28rpx;
					margin-right: 10rpx;
				}
				text{
					margin-left: 10rpx;
				}
			}
		}
		.login_other{
			width: 690rpx;
			margin-top: 180rpx;
			position: relative;
			z-index: 2;
			.login_other_title{
				width: 690rpx;
				view{
					width: 220rpx;
					height: 1px;
					background-color: #979797;
				}
			}
			.login_other_image{
				width: 690rpx;
				margin-top: 40rpx;
				view{
					width: 88rpx;
					height: 88rpx;
					position: relative;
					image{
						width: 88rpx;
						height: 88rpx;
					}
					button{
						width: 88rpx;
						height: 88rpx;
						position: absolute;
						top: 0;
						left: 0;
						opacity: 0;
						z-index: 2;
					}
				}
				
			}
		}
	}
</style>
