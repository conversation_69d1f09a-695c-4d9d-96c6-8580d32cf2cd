# 服务器配置更新指令

## 🎯 问题分析

服务器上的`docker-compose.yml`使用的是旧版本配置，缺少以下关键功能：
1. **定时任务支持** - 没有cron服务配置
2. **网络配置** - 缺少Docker网络设置
3. **健康检查** - 缺少Redis健康检查
4. **环境变量** - 缺少生产环境配置

## 🔧 解决方案

### 步骤1：备份现有配置
```bash
# 登录服务器
ssh root@*************

# 备份现有配置
cd /root/local
cp docker-compose.yml docker-compose.yml.backup.$(date +%Y%m%d_%H%M%S)
```

### 步骤2：替换配置文件
将本地的 `docker-compose.server-final.yml` 内容复制到服务器的 `docker-compose.yml`：

```bash
# 在服务器上编辑配置文件
vim /root/local/docker-compose.yml
```

**新的配置内容**：
```yaml
version: '3.8'

services:
  web:
    build:
      context: ./heaven
      dockerfile: Dockerfile.richarvey
    container_name: heaven-web
    restart: unless-stopped
    ports:
      - "8081:80"
    volumes:
      - ./heaven/storage:/var/www/html/storage
      - ./heaven/public/storage:/var/www/html/public/storage
      - ./heaven/bootstrap/cache:/var/www/html/bootstrap/cache
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - APP_URL=http://*************:8081
      - DB_CONNECTION=mysql
      - DB_HOST=*************
      - DB_PORT=3306
      - DB_DATABASE=heaven
      - DB_USERNAME=yunyitang
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - CACHE_DRIVER=redis
      - SESSION_DRIVER=redis
      - QUEUE_CONNECTION=redis
    depends_on:
      - redis
    networks:
      - heaven-network

  redis:
    image: redis:alpine
    container_name: heaven-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - heaven-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      retries: 3
      timeout: 5s
      interval: 30s

volumes:
  redis_data:
    driver: local

networks:
  heaven-network:
    driver: bridge
```

### 步骤3：重启服务
```bash
# 停止现有服务
cd /root/local
docker-compose down

# 重新构建并启动服务
docker-compose up -d --build

# 等待服务启动
sleep 15

# 检查服务状态
docker-compose ps
```

### 步骤4：验证定时任务
```bash
# 检查容器状态
docker ps | grep heaven

# 进入容器检查cron
docker exec -it heaven-web sh

# 在容器内检查crontab
crontab -l

# 检查Laravel定时任务配置
php artisan schedule:list

# 手动执行一次调度器
php artisan schedule:run

# 检查cron日志
tail -f /var/log/cron.log

# 退出容器
exit
```

## 🔍 验证步骤

### 1. 检查服务状态
```bash
# 检查容器运行状态
docker-compose ps

# 应该看到两个容器都是Up状态：
# heaven-web    Up    0.0.0.0:8081->80/tcp
# heaven-redis  Up    0.0.0.0:6379->6379/tcp
```

### 2. 检查定时任务
```bash
# 检查定时任务是否正常执行
docker exec heaven-web php artisan schedule:list

# 应该看到类似输出：
# */30 * * * *  php artisan autoInsert:commentsAndGoods
# */59 * * * *  php artisan autoInsert:commentsAndGoods  
# */10 * * * *  php artisan video:query-status
```

### 3. 检查应用访问
- **管理后台**: http://*************:8081/admin
- **H5前端**: http://*************:8080
- **API接口**: http://*************:8081/api

## 🚨 注意事项

1. **数据库密码**：确保在`.env`文件中设置正确的数据库密码
2. **存储权限**：确保storage目录有正确的写入权限
3. **定时任务日志**：定时任务日志会写入`/var/log/cron.log`
4. **Redis数据**：Redis数据会持久化到`redis_data`卷中

## 🔧 故障排除

### 如果定时任务不执行：
```bash
# 进入容器
docker exec -it heaven-web sh

# 检查cron进程
ps aux | grep cron

# 重启cron服务
killall crond
crond -f -d 8 &

# 手动执行调度器测试
php artisan schedule:run
```

### 如果容器启动失败：
```bash
# 查看容器日志
docker-compose logs heaven-web

# 重新构建镜像
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

## ✅ 完成确认

更新完成后，您应该能够：
1. ✅ 访问管理后台和H5前端
2. ✅ 看到定时任务正常执行
3. ✅ 视频状态查询自动运行
4. ✅ Redis缓存正常工作
