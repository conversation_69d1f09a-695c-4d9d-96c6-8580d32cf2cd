<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\AiModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class AiModelController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new AiModel(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('model_name', '大模型名称');
            $grid->column('domain', '域名');
            $grid->column('access_key', 'AccessKey')->limit(20);
            $grid->column('ai_model_name', '模型名称');
            $grid->column('video_mode', '视频模式');
            $grid->column('is_enabled', '是否启用')->switch();
            $grid->column('created_at', '创建时间');
            $grid->column('updated_at', '更新时间')->sortable();

            $grid->disableViewButton(); // 禁用详情按钮
            $grid->showColumnSelector(); // 开启字段选择器功能
            $grid->paginate(10);
            $grid->model()->orderBy('id', 'desc');
            $grid->quickSearch(['model_name', 'domain', 'ai_model_name'])->placeholder('搜索...'); //快捷搜索

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id')->width(3);
                $filter->like('model_name', '大模型名称')->width(3);
                $filter->like('domain', '域名')->width(3);
                $filter->equal('is_enabled', '是否启用')->select([0 => '禁用', 1 => '启用'])->width(3);
                $filter->between('created_at', '创建时间')->datetime()->width(3);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new AiModel(), function (Show $show) {
            $show->field('id');
            $show->field('model_name', '大模型名称');
            $show->field('domain', '域名');
            $show->field('access_key', 'AccessKey');
            $show->field('secret_key', 'SecretKey');
            $show->field('ai_model_name', '模型名称');
            $show->field('video_mode', '视频模式');
            $show->field('callback_url', '回调通知地址');
            $show->field('is_enabled', '是否启用')->using([0 => '禁用', 1 => '启用']);
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new AiModel(), function (Form $form) {
            $form->display('id');
            $form->text('model_name', '大模型名称')->required();
            $form->url('domain', '域名')->required();
            $form->text('access_key', 'AccessKey')->required();
            $form->text('secret_key', 'SecretKey')->required();
            $form->text('ai_model_name', '模型名称')->required();
            $form->select('video_mode', '视频模式')
                ->options([
                    'pro' => '专业模式',
                    'quality' => '质量模式',
                    'standard' => '标准模式',
                    'fast' => '快速模式'
                ])
                ->required();
            $form->url('callback_url', '回调通知地址');
            $form->switch('is_enabled', '是否启用')->default(0);

            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }
}
