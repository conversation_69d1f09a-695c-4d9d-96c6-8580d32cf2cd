version: '3.8'

services:
  web:
    # 使用服务器上已有的镜像，不进行构建
    image: heaven-web-image:latest
    container_name: heaven-web
    restart: unless-stopped
    ports:
      - "8081:80"
    volumes:
      - ./heaven:/var/www/html
      - ./heaven/storage:/var/www/html/storage
      - ./heaven/public/storage:/var/www/html/public/storage
      - ./heaven/bootstrap/cache:/var/www/html/bootstrap/cache
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - APP_URL=http://*************:8081
      # 数据库配置 - 使用服务器本地MySQL
      - DB_CONNECTION=mysql
      - DB_HOST=*************
      - DB_PORT=3306
      - DB_DATABASE=heaven
      - DB_USERNAME=yunyitang
      # Redis配置 - 如果有Redis镜像就用，没有就用文件缓存
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      # 缓存配置
      - CACHE_DRIVER=file
      - SESSION_DRIVER=file
      - QUEUE_CONNECTION=sync
    networks:
      - heaven-network

  # 只有在服务器上有Redis镜像时才启用这个服务
  # redis:
  #   image: redis:latest  # 或其他可用的Redis镜像
  #   container_name: heaven-redis
  #   restart: unless-stopped
  #   command: redis-server --appendonly yes
  #   volumes:
  #     - redis_data:/data
  #   ports:
  #     - "6379:6379"
  #   networks:
  #     - heaven-network

volumes:
  redis_data:
    driver: local

networks:
  heaven-network:
    driver: bridge
