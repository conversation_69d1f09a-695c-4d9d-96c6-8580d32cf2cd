version: '3.8'

services:
  web:
    build:
      context: ./heaven
      dockerfile: Dockerfile.richarvey
    container_name: heaven-web
    restart: unless-stopped
    ports:
      - "8081:80"
    volumes:
      - ./heaven/storage:/var/www/html/storage
      - ./heaven/public/storage:/var/www/html/public/storage
      - ./heaven/bootstrap/cache:/var/www/html/bootstrap/cache
    environment:
      # 数据库配置 - 使用服务器本地MySQL
      - DB_CONNECTION=mysql
      - DB_HOST=*************
      - DB_PORT=3306
      - DB_DATABASE=heaven
      - DB_USERNAME=yunyitang
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      # 缓存配置
      - CACHE_DRIVER=redis
      - SESSION_DRIVER=redis
      - QUEUE_CONNECTION=redis
    depends_on:
      - redis
    networks:
      - heaven-network

  redis:
    # 使用服务器上已有的Redis镜像，避免网络下载
    image: heaven-redis-image:latest  # 如果服务器上有其他版本，请相应修改
    container_name: heaven-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - heaven-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      retries: 3
      timeout: 5s
      interval: 30s

volumes:
  redis_data:
    driver: local

networks:
  heaven-network:
    driver: bridge
