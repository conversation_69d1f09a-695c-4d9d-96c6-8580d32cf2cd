version: '3.8'

services:
  # Web 服务
  web:
    build:
      context: .
      dockerfile: Dockerfile.richarvey
    container_name: heaven-web
    restart: always
    ports:
      - "8081:80"
    volumes:
      - ./heaven:/var/www/html
      - ./heaven/storage:/var/www/html/storage
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_CONNECTION=mysql
      - DB_HOST=your_remote_mysql_host
      - DB_PORT=3306
      - DB_DATABASE=heaven
      - DB_USERNAME=your_remote_mysql_username
      - DB_PASSWORD=your_remote_mysql_password
      - REDIS_HOST=redis
      - REDIS_PASSWORD=null
      - REDIS_PORT=6379
    depends_on:
      - redis
    networks:
      - heaven-network

  # Redis 服务
  redis:
    image: heaven-redis-image:latest
    container_name: heaven-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - heaven-network

networks:
  heaven-network:
    driver: bridge

volumes:
  redis_data: 