#!/bin/sh
set -e

echo "Starting Heaven application in offline mode..."

# 设置目录权限
chown -R www-data:www-data /var/www/html/storage /var/www/html/bootstrap/cache
chmod -R 777 /var/www/html/storage
chmod -R 777 /var/www/html/bootstrap/cache

# 如果有.env文件，使用它；否则复制示例文件
if [ ! -f "/var/www/html/.env" ]; then
    if [ -f "/var/www/html/.env.example" ]; then
        cp /var/www/html/.env.example /var/www/html/.env
        echo "Copied .env.example to .env"
    fi
fi

echo "Heaven application setup completed"
echo "Application is running in maintenance mode"
echo "Please install PHP, Nginx, and other dependencies manually"

# 保持容器运行
exec "$@"
