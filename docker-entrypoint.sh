#!/bin/sh
set -e

echo "=== Heaven Application Starting ==="

# 检查Redis连接（可选，如果Redis不可用则跳过）
echo "Checking Redis connection..."
if nc -z redis 6379 2>/dev/null; then
    echo "Redis is available"
else
    echo "Redis is not available, continuing without Redis"
fi

# 设置正确的权限
echo "Setting up permissions..."
chown -R nginx:nginx /var/www/html
find /var/www/html -type d -exec chmod 755 {} \;
find /var/www/html -type f -exec chmod 644 {} \;
chmod -R 777 /var/www/html/storage
chmod -R 777 /var/www/html/bootstrap/cache

# 检查并创建.env文件
if [ ! -f "/var/www/html/.env" ]; then
    echo "Creating .env file..."
    cp /var/www/html/.env.example /var/www/html/.env
    php artisan key:generate --force
fi

# 安装Composer依赖（如果需要）
if [ ! -f "/var/www/html/vendor/autoload.php" ]; then
    echo "Installing Composer dependencies..."
    composer install --no-interaction --no-dev --optimize-autoloader
fi

# Laravel应用初始化
echo "Initializing Laravel application..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

# 创建存储链接
php artisan storage:link 2>/dev/null || echo "Storage link already exists"

# 运行数据库迁移（如果数据库可用）
echo "Running database migrations..."
php artisan migrate --force 2>/dev/null || echo "Database migration skipped"

# 设置Laravel定时任务
echo "Setting up cron jobs..."
echo "* * * * * cd /var/www/html && php artisan schedule:run >> /var/log/cron.log 2>&1" | crontab -
echo "Crontab configured:"
crontab -l

# 启动cron服务
echo "Starting cron daemon..."
crond -f -d 8 &

# 最终权限检查
chown -R nginx:nginx /var/www/html
chmod 644 /var/www/html/public/index.php

echo "=== Starting services ==="

# 启动PHP-FPM
echo "Starting PHP-FPM..."
php-fpm -D

# 启动Nginx
echo "Starting Nginx..."
exec nginx -g 'daemon off;'