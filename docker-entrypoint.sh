#!/bin/sh
set -e

# 等待 Redis 服务启动
echo "Waiting for Redis..."
while ! nc -z redis 6379; do
  sleep 1
done
echo "Redis is ready!"

# 安装项目依赖
if [ ! -f "vendor/autoload.php" ]; then
    echo "Installing dependencies..."
    composer install --no-interaction --no-scripts
fi

# 生成应用密钥
if [ ! -f ".env" ]; then
    cp .env.example .env
    php artisan key:generate
fi

# 运行数据库迁移
echo "Running migrations..."
php artisan migrate --force

# 创建存储链接
php artisan storage:link

# 设置目录权限
chown -R nginx:nginx /var/www/html/storage /var/www/html/bootstrap/cache
chmod -R 777 /var/www/html/storage
chmod -R 777 /var/www/html/bootstrap/cache

# 设置Laravel定时任务
echo "Setting up Laravel scheduler..."
echo "* * * * * cd /var/www/html && php artisan schedule:run >> /var/log/cron.log 2>&1" | crontab -
echo "Crontab installed:"
crontab -l

# 启动 cron 服务
echo "Starting cron service..."
crond -f -d 8 &

# 启动 PHP-FPM
php-fpm -D

# 启动 nginx (前台运行)
exec nginx -g 'daemon off;'