#!/bin/bash

echo "🔧 修复Laravel缓存路径问题..."

# 检查是否在正确的目录
if [ ! -f "docker-compose.server.yml" ]; then
    echo "❌ 请在部署目录中运行此脚本"
    exit 1
fi

# 1. 停止容器
echo "⏹️ 停止容器..."
docker-compose -f docker-compose.server.yml down

# 2. 创建必要的目录
echo "📁 创建Laravel必要目录..."
mkdir -p heaven/bootstrap/cache
mkdir -p heaven/storage/app
mkdir -p heaven/storage/framework/cache
mkdir -p heaven/storage/framework/sessions
mkdir -p heaven/storage/framework/views
mkdir -p heaven/storage/logs

# 3. 设置正确的权限
echo "🔐 设置目录权限..."
chmod -R 775 heaven/storage
chmod -R 775 heaven/bootstrap/cache

# 4. 重新启动容器
echo "▶️ 重新启动容器..."
docker-compose -f docker-compose.server.yml up -d

# 5. 等待容器启动
echo "⏳ 等待容器启动..."
sleep 20

# 6. 检查容器状态
echo "🔍 检查容器状态..."
docker ps | grep heaven

# 7. 在容器内设置权限
echo "🔐 在容器内设置权限..."
docker exec heaven-web chown -R nginx:nginx /var/www/html/storage
docker exec heaven-web chown -R nginx:nginx /var/www/html/bootstrap/cache
docker exec heaven-web chmod -R 775 /var/www/html/storage
docker exec heaven-web chmod -R 775 /var/www/html/bootstrap/cache

# 8. 清理Laravel缓存
echo "🧹 清理Laravel缓存..."
docker exec heaven-web php artisan config:clear
docker exec heaven-web php artisan cache:clear
docker exec heaven-web php artisan view:clear
docker exec heaven-web php artisan route:clear

# 9. 重新生成缓存
echo "🔄 重新生成缓存..."
docker exec heaven-web php artisan config:cache
docker exec heaven-web php artisan route:cache
docker exec heaven-web php artisan view:cache

echo "✅ 缓存路径修复完成！"
echo "🌐 请访问: http://$(hostname -I | awk '{print $1}'):8081/admin"
