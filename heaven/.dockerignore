# Git files
.git
.gitignore
.gitattributes

# IDE files
.vscode
.idea
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Development files
node_modules
npm-debug.log
yarn-error.log
.env.local
.env.development
.env.testing

# Laravel specific
storage/logs/*
!storage/logs/.gitkeep
storage/debugbar/*
!storage/debugbar/.gitkeep
storage/framework/cache/*
!storage/framework/cache/.gitkeep
storage/framework/sessions/*
!storage/framework/sessions/.gitkeep
storage/framework/testing/*
!storage/framework/testing/.gitkeep
storage/framework/views/*
!storage/framework/views/.gitkeep

# Temporary files
*.tmp
*.temp
*.log

# Documentation files
README.md
DEPLOYMENT.md
DOCKER_COMPOSE_GUIDE.md
DOCKER_CONFIG_SUMMARY.md
QUICK_DEPLOY.md
*.md

# Build and deployment files
deploy-package/
docker-images/
heaven-web-image.tar.gz
build-image.sh
deploy.sh
deploy-to-server.sh
update-server.sh
status.sh

# Test files
tests/
phpunit.xml
.phpunit.result.cache

# Backup files
*.bak
*.backup

# Docker files (we don't need these in the image)
docker-compose*.yml
Dockerfile*

# Unnecessary directories
heaven/
docker/
scripts/
conf/

# Text files
*.txt
!robots.txt

# Exclude root index.php (phpinfo file that interferes with Laravel)
index.php
!public/index.php
