APP_NAME="Heaven Backend"
APP_ENV=production
APP_KEY=base64:your-app-key-here
APP_DEBUG=false
APP_URL=https://yourdomain.com

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# 数据库配置（使用外部MySQL服务器）
DB_CONNECTION=mysql
DB_HOST=your-mysql-server-ip
DB_PORT=3306
DB_DATABASE=heaven
DB_USERNAME=heaven_user
DB_PASSWORD=your-secure-password

# Redis配置
REDIS_HOST=redis
REDIS_PASSWORD=your-redis-password
REDIS_PORT=6379

# 缓存配置
BROADCAST_DRIVER=log
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=redis
SESSION_LIFETIME=120

# 邮件配置
MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# 第三方服务配置
WECHAT_OFFICIAL_ACCOUNT_APPID=your-wechat-appid
WECHAT_OFFICIAL_ACCOUNT_SECRET=your-wechat-secret
WECHAT_OFFICIAL_ACCOUNT_TOKEN=your-wechat-token

# 支付配置
ALIPAY_APPID=your-alipay-appid
ALIPAY_PUBLIC_KEY=your-alipay-public-key
ALIPAY_PRIVATE_KEY=your-alipay-private-key

# 短信配置
SMS_DRIVER=aliyun
ALIYUN_ACCESS_KEY_ID=your-aliyun-access-key
ALIYUN_ACCESS_KEY_SECRET=your-aliyun-secret

# 对象存储配置
COS_SECRET_ID=your-cos-secret-id
COS_SECRET_KEY=your-cos-secret-key
COS_REGION=your-cos-region
COS_BUCKET=your-cos-bucket

# AI模型配置
VOLCANO_ENGINE_API_KEY=your-volcano-api-key
VOLCANO_ENGINE_SECRET_KEY=your-volcano-secret-key
