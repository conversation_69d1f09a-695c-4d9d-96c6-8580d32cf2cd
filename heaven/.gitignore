# Laravel
/node_modules
/public/build
/public/hot
/public/storage
/storage/*.key
/vendor
.env
.env.backup
.env.production
.env.staging
.env.dev
.phpunit.result.cache
Homestead.json
Homestead.yaml
auth.json
npm-debug.log
yarn-error.log
/.idea
/.vscode

# Logs
/storage/logs/*.log
*.log

# Cache
/bootstrap/cache/*
!/bootstrap/cache/.gitkeep

# Uploads
/public/uploads/*
!/public/uploads/.gitkeep

# Docker
docker-compose.override.yml

# OS
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak
*.backup
