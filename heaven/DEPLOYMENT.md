# Heaven Backend 部署指南

## 概述

本文档详细说明如何将Heaven Backend项目部署到生产服务器。

## 系统要求

### 服务器配置
- **操作系统**: Ubuntu 20.04 LTS 或更高版本
- **CPU**: 最少2核心，推荐4核心
- **内存**: 最少4GB，推荐8GB
- **存储**: 最少50GB SSD
- **网络**: 公网IP地址

### 软件要求
- Docker 20.10+
- Docker Compose 1.29+
- Git
- Nginx (可选，用于反向代理)

## 部署步骤

### 1. 服务器环境准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要软件
sudo apt install -y curl git unzip

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 将用户添加到docker组
sudo usermod -aG docker $USER
```

### 2. 获取项目代码

```bash
# 克隆项目
git clone <your-repository-url> /opt/heaven
cd /opt/heaven

# 或者上传代码包
scp -r ./ai_heaven user@server:/opt/heaven
```

### 3. 配置环境变量

```bash
# 复制环境配置文件
cp .env.production .env

# 编辑环境变量
nano .env
```

**重要配置项**:
- `APP_URL`: 设置为您的域名
- `DB_PASSWORD`: 设置强密码
- `REDIS_PASSWORD`: 设置Redis密码
- 各种API密钥和配置

### 4. 设置SSL证书（可选但推荐）

```bash
# 运行SSL设置脚本
./scripts/setup-ssl.sh yourdomain.com <EMAIL>
```

### 5. 部署应用

```bash
# 运行部署脚本
./deploy.sh production deploy
```

### 6. 验证部署

```bash
# 检查容器状态
docker-compose -f docker-compose.prod.yml ps

# 检查应用健康状态
curl http://localhost/health

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f
```

## 常用操作

### 更新应用
```bash
./deploy.sh production update
```

### 重启服务
```bash
./deploy.sh production restart
```

### 查看日志
```bash
./deploy.sh production logs
```

### 停止服务
```bash
./deploy.sh production stop
```

## 监控和维护

### 设置监控
```bash
# 添加监控脚本到crontab
echo "*/5 * * * * /opt/heaven/scripts/monitor.sh" | crontab -
```

### 备份策略
- 数据库自动备份（每天凌晨2点）
- 备份文件保留7天
- 建议定期备份到远程存储

### 日志管理
- 应用日志: `/opt/heaven/storage/logs/`
- Nginx日志: `/var/log/nginx/`
- 系统日志: `/var/log/`

## 安全配置

### 防火墙设置
```bash
# 安装ufw
sudo apt install ufw

# 配置防火墙规则
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 定期更新
```bash
# 设置自动安全更新
sudo apt install unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades
```

## 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看详细错误信息
   docker-compose -f docker-compose.prod.yml logs
   
   # 重新构建镜像
   docker-compose -f docker-compose.prod.yml build --no-cache
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库容器状态
   docker exec -it heaven-mysql-prod mysql -u root -p
   
   # 检查网络连接
   docker network ls
   ```

3. **SSL证书问题**
   ```bash
   # 重新获取证书
   sudo certbot renew --force-renewal
   
   # 检查证书状态
   sudo certbot certificates
   ```

### 性能优化

1. **数据库优化**
   - 定期优化数据库表
   - 监控慢查询
   - 适当增加内存配置

2. **缓存优化**
   - 启用Redis持久化
   - 配置适当的缓存策略
   - 监控缓存命中率

3. **Web服务优化**
   - 启用Gzip压缩
   - 配置静态文件缓存
   - 使用CDN加速

## 联系支持

如果在部署过程中遇到问题，请：
1. 查看日志文件
2. 检查系统资源使用情况
3. 联系技术支持团队
