# Docker Compose 配置文件说明

本项目包含多个Docker Compose配置文件，用于不同的部署场景。

## 配置文件说明

### 1. `docker-compose.yml` (Laravel Sail - 开发环境)
- **用途**: Laravel Sail开发环境
- **包含服务**: Laravel应用 + Redis
- **数据库**: 使用外部MySQL服务器
- **适用场景**: 本地开发

```bash
# 启动开发环境
./vendor/bin/sail up -d
```

### 2. `docker-compose.richarvey.yml` (当前使用)
- **用途**: 使用richarvey/nginx-php-fpm镜像的配置
- **包含服务**: Web应用 + Redis
- **数据库**: 使用外部MySQL服务器 (82.156.98.152)
- **适用场景**: 当前开发/测试环境

```bash
# 启动当前环境
docker-compose -f docker-compose.richarvey.yml up -d

# 停止服务
docker-compose -f docker-compose.richarvey.yml down
```

### 3. `docker-compose.prod.yml` (生产环境)
- **用途**: 生产环境部署
- **包含服务**: Web应用 + Redis + Nginx反向代理
- **数据库**: 使用外部MySQL服务器
- **适用场景**: 生产服务器部署

```bash
# 生产环境部署
./deploy.sh production deploy

# 或手动启动
docker-compose -f docker-compose.prod.yml up -d
```

## 数据库配置

所有配置文件都使用外部MySQL服务器，需要在`.env`文件中配置：

```env
# 数据库配置
DB_CONNECTION=mysql
DB_HOST=your-mysql-server-ip  # 外部MySQL服务器IP
DB_PORT=3306
DB_DATABASE=heaven
DB_USERNAME=heaven_user
DB_PASSWORD=your-secure-password
```

## 服务端口映射

| 配置文件 | Web端口 | Redis端口 | Nginx端口 |
|---------|---------|-----------|-----------|
| docker-compose.yml | 80 | 6379 | - |
| docker-compose.richarvey.yml | 8081 | 6379 | - |
| docker-compose.prod.yml | 80, 443 | 6379 | 8080 |

## 常用命令

```bash
# 查看服务状态
docker-compose -f <compose-file> ps

# 查看日志
docker-compose -f <compose-file> logs -f

# 重启服务
docker-compose -f <compose-file> restart

# 进入容器
docker-compose -f <compose-file> exec web bash

# 执行Laravel命令
docker-compose -f <compose-file> exec web php artisan <command>
```

## 注意事项

1. **MySQL移除**: 所有配置文件都已移除MySQL容器，使用外部MySQL服务器
2. **数据持久化**: Redis数据通过Docker卷持久化
3. **网络配置**: 所有服务在同一个Docker网络中通信
4. **环境变量**: 不同环境使用不同的`.env`文件配置

## 迁移说明

如果需要从包含MySQL的配置迁移到当前配置：

1. 备份MySQL数据
2. 在外部MySQL服务器上恢复数据
3. 更新`.env`文件中的数据库配置
4. 使用新的docker-compose文件启动服务
