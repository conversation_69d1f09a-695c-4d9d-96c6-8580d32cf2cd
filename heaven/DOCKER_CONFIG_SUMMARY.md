# Docker 配置文件说明

## 📋 当前配置文件

项目现在只保留了两个必要的Docker配置文件：

### 1. `docker-compose.richarvey.yml` - 本地开发环境
- **用途**: 本地开发和测试
- **特点**: 
  - 使用Dockerfile构建镜像
  - 包含完整的开发环境配置
  - 支持热重载和调试
- **使用方法**:
  ```bash
  docker-compose -f docker-compose.richarvey.yml up -d --build
  ```

### 2. `docker-compose.server.yml` - 服务器生产环境
- **用途**: 服务器生产部署
- **特点**:
  - 使用预构建的镜像 `heaven-web-image:latest`
  - 生产环境优化配置
  - 不需要构建，直接使用镜像
- **使用方法**:
  ```bash
  docker-compose -f docker-compose.server.yml up -d
  ```

## 🗑️ 已删除的配置文件

以下配置文件已被删除，因为功能重复或不再需要：

- ❌ `docker-compose-server.yml` - 旧的服务器配置
- ❌ `docker-compose.prod.yml` - 重复的生产环境配置
- ❌ `docker-compose.yml` - 基础配置（功能已合并）
- ❌ `docker-compose.server-final.yml` - 临时配置文件
- ❌ `docker-compose.local-images.yml` - 临时配置文件
- ❌ `docker-compose.server-offline.yml` - 临时配置文件

## 🎯 使用场景

### 本地开发
```bash
# 启动开发环境
docker-compose -f docker-compose.richarvey.yml up -d --build

# 查看状态
docker-compose -f docker-compose.richarvey.yml ps

# 查看日志
docker-compose -f docker-compose.richarvey.yml logs -f heaven-web

# 停止服务
docker-compose -f docker-compose.richarvey.yml down
```

### 服务器部署
```bash
# 启动生产环境（需要先有镜像）
docker-compose -f docker-compose.server.yml up -d

# 查看状态
docker-compose -f docker-compose.server.yml ps

# 查看日志
docker-compose -f docker-compose.server.yml logs -f heaven-web

# 停止服务
docker-compose -f docker-compose.server.yml down
```

## 🔧 配置差异

| 配置项 | 本地开发 | 服务器生产 |
|--------|----------|------------|
| **镜像来源** | 构建 (Dockerfile) | 预构建镜像 |
| **环境变量** | `APP_ENV=local` | `APP_ENV=production` |
| **调试模式** | `APP_DEBUG=true` | `APP_DEBUG=false` |
| **缓存驱动** | Redis | Redis |
| **重启策略** | `always` | `unless-stopped` |

## 📁 文件结构

```
project/
├── docker-compose.richarvey.yml    # 本地开发配置
├── docker-compose.server.yml       # 服务器生产配置
├── Dockerfile.richarvey            # 构建镜像用
├── docker-entrypoint.sh           # 容器启动脚本
├── nginx-default.conf              # Nginx配置
└── zzz-custom.conf                 # PHP-FPM配置
```

## ✅ 优化效果

1. **简化项目结构** - 从5个yml文件减少到2个
2. **明确使用场景** - 开发和生产环境配置分离
3. **减少维护成本** - 不再有重复和冗余的配置
4. **提高可读性** - 配置文件命名更加清晰
5. **统一文档** - README和QUICK_DEPLOY文档已同步更新

## 🚀 下一步

1. **本地开发**: 使用 `docker-compose.richarvey.yml`
2. **构建镜像**: 使用 `build-image.sh` 脚本
3. **服务器部署**: 使用 `docker-compose.server.yml`
4. **监控维护**: 使用 `deploy.sh` 和 `status.sh` 脚本

现在项目配置更加清晰和易于维护！🎉
