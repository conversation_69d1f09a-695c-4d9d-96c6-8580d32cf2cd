FROM richarvey/nginx-php-fpm:latest

WORKDIR /var/www/html

# 安装必要的包和PHP扩展
RUN apk add --no-cache \
    git \
    curl \
    libpng-dev \
    libxml2-dev \
    zip \
    unzip \
    oniguruma-dev \
    nodejs \
    npm \
    dcron \
    busybox-suid \
    mysql-client \
    redis \
    && docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd \
    && curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# 复制应用代码
COPY . /var/www/html/

# 删除可能干扰Laravel的根目录index.php文件
RUN rm -f /var/www/html/index.php

# 安装PHP依赖（生产环境优化）
RUN composer install --no-dev --optimize-autoloader --no-interaction --no-progress --prefer-dist \
    && composer clear-cache

# 清理缓存和临时文件
RUN rm -rf storage/logs/* \
    && rm -rf storage/debugbar/* \
    && rm -rf storage/framework/cache/* \
    && rm -rf storage/framework/sessions/* \
    && rm -rf storage/framework/views/* \
    && rm -rf bootstrap/cache/*

# 复制配置文件
COPY nginx-default.conf /etc/nginx/sites-available/default.conf
COPY zzz-custom.conf /usr/local/etc/php-fpm.d/zzz-custom.conf

# PHP配置优化
RUN echo "upload_max_filesize=50M" >> /usr/local/etc/php/conf.d/docker-php-ext-upload.ini \
    && echo "post_max_size=50M" >> /usr/local/etc/php/conf.d/docker-php-ext-upload.ini \
    && echo "memory_limit=512M" >> /usr/local/etc/php/conf.d/docker-php-ext-upload.ini \
    && echo "max_execution_time=300" >> /usr/local/etc/php/conf.d/docker-php-ext-upload.ini \
    && echo "max_input_vars=3000" >> /usr/local/etc/php/conf.d/docker-php-ext-upload.ini

# 创建必要的目录和设置权限
RUN mkdir -p /var/www/html/storage \
             /var/www/html/bootstrap/cache \
             /var/www/html/public \
             /var/log/cron \
    && chown -R nginx:nginx /var/www/html \
    && find /var/www/html -type d -exec chmod 755 {} \; \
    && find /var/www/html -type f -exec chmod 644 {} \; \
    && chmod -R 777 /var/www/html/storage \
    && chmod -R 777 /var/www/html/bootstrap/cache \
    && chmod 644 /var/www/html/public/index.php

EXPOSE 80

# 复制启动脚本
COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

ENTRYPOINT ["docker-entrypoint.sh"]
CMD ["/bin/sh", "-c", "php-fpm -D && nginx -g 'daemon off;'"]
