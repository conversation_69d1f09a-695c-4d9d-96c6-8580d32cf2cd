FROM richarvey/nginx-php-fpm:latest

# Install additional PHP extensions and cron
RUN apk add --no-cache \
    autoconf \
    dpkg-dev dpkg \
    file \
    g++ \
    gcc \
    libc-dev \
    make \
    pkgconf \
    re2c \
    dcron

WORKDIR /var/www/html

# Copy custom PHP configuration
COPY heaven/scripts/php.ini /usr/local/etc/php/conf.d/custom.ini

# Create crontab file
RUN echo "* * * * * cd /var/www/html && php artisan schedule:run >> /dev/null 2>&1" > /etc/crontabs/root

# Copy start script
COPY heaven/scripts/start.sh /start.sh
RUN chmod +x /start.sh

EXPOSE 80 443

CMD ["/start.sh"] 