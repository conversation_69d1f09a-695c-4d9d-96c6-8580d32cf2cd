# Heaven 快速部署指南

## 🚀 一键部署到服务器

### 方案一：在线部署（推荐）

**前提条件**：
- 服务器可以访问互联网
- 已安装Docker和Docker Compose

```bash
# 1. 上传项目到服务器
scp -r heaven/ root@*************:/var/local/heaven/

# 2. 登录服务器并配置
ssh root@*************
cd /var/local/heaven/

# 3. 配置环境变量
# cp .env.example .env
# vim .env  # 修改数据库、Redis、COS等配置

# 4. 一键部署
./deploy.sh production deploy

# 5. 检查状态
./status.sh
```

### 方案二：离线部署

**适用场景**：
- 服务器无法访问互联网
- 需要在内网环境部署

#### 步骤1：本地准备（有网络的机器）

```bash
# 导出Docker镜像
./docker-export.sh export

# 打包项目文件
./docker-export.sh package

# 检查导出文件
ls -lh docker-images/
# heaven-project.tar.gz  (53MB)
# redis_alpine.tar.gz    (23MB)
# images.list
```

#### 步骤2：上传到服务器

```bash
# 方式1：使用脚本自动部署
./docker-export.sh deploy --server-ip *************

# 方式2：手动上传
scp docker-images/* root@*************:/tmp/
scp deploy.sh status.sh root@*************:/root/
```

#### 步骤3：服务器部署

```bash
# 登录服务器
ssh root@*************

# 解压项目
cd /root
tar -xzf /tmp/heaven-project.tar.gz

# 导入Docker镜像
cd heaven
./docker-export.sh import

# 配置环境
cp .env.example .env
vim .env  # 修改配置

# 启动服务
./deploy.sh production deploy
```

## 📋 配置清单

### 必须配置的环境变量

```env
# 应用配置
APP_URL=http://*************:8081

# 数据库配置
DB_HOST=*************
DB_DATABASE=heaven
DB_USERNAME=yunyitang
DB_PASSWORD=your_password

# 对象存储配置
COS_REGION=ap-beijing
COS_BUCKET=mk-1300059703
COS_SECRET_ID=your_secret_id
COS_SECRET_KEY=your_secret_key

# AI模型配置
VOLCANO_ENGINE_DOMAIN=https://ark.cn-beijing.volces.com/api/v3
VOLCANO_ENGINE_ACCESS_KEY=your_access_key
```

## 🔧 常用操作

### 服务管理
```bash
# 查看状态
./status.sh

# 重启服务
./deploy.sh production restart

# 查看日志
./deploy.sh production logs

# 停止服务
./deploy.sh production stop
```

### 应用管理
```bash
# 进入容器
docker exec -it heaven-web bash

# 清理缓存
docker exec -it heaven-web php artisan cache:clear

# 手动执行定时任务
docker exec -it heaven-web php artisan video:query-status

# 查看队列状态
docker exec -it heaven-web php artisan queue:work --once
```

### 数据库管理
```bash
# 运行迁移
docker exec -it heaven-web php artisan migrate

# 创建管理员
docker exec -it heaven-web php artisan db:seed --class=AdminUserSeeder

# 数据库备份
mysqldump -h ************* -u yunyitang -p heaven > backup.sql
```

## 🌐 访问地址

部署完成后，可以通过以下地址访问：

- **管理后台**: http://*************:8081/admin
  - 默认账号：admin
  - 默认密码：admin

- **H5前端**: http://*************:8080

- **API接口**: http://*************:8081/api

## 🔍 故障排除

### 容器启动失败
```bash
# 本地开发环境
docker-compose -f docker-compose.richarvey.yml logs heaven-web
docker-compose -f docker-compose.richarvey.yml down
docker-compose -f docker-compose.richarvey.yml up -d --build

# 服务器生产环境
docker-compose -f docker-compose.server.yml logs heaven-web
docker-compose -f docker-compose.server.yml down
docker-compose -f docker-compose.server.yml up -d
```

### 数据库连接失败
```bash
# 测试数据库连接
docker exec -it heaven-web php artisan tinker
# 在tinker中执行：DB::connection()->getPdo();
```

### 视频生成失败
```bash
# 查看视频日志
docker exec -it heaven-web tail -f storage/logs/video/video-$(date +%Y-%m-%d).log

# 检查AI模型配置
docker exec -it heaven-web php artisan tinker
# 在tinker中执行：\App\AI\Models\AiModel::where('is_enabled', true)->get();
```

### 定时任务不执行
```bash
# 检查定时任务配置
docker exec -it heaven-web php artisan schedule:list

# 手动运行调度器
docker exec -it heaven-web php artisan schedule:run

# 检查cron服务
docker exec -it heaven-web ps aux | grep cron
```

## 📞 技术支持

如果遇到问题，请检查：

1. **日志文件**：`storage/logs/` 目录下的相关日志
2. **容器状态**：使用 `./status.sh` 检查服务状态
3. **网络连接**：确保服务器可以访问外部API
4. **权限设置**：确保 `storage` 和 `bootstrap/cache` 目录可写

## 🎯 性能优化

### 生产环境优化
```bash
# 启用OPcache
docker exec -it heaven-web php -m | grep OPcache

# 优化Composer自动加载
docker exec -it heaven-web composer dump-autoload --optimize

# 缓存配置
docker exec -it heaven-web php artisan config:cache
docker exec -it heaven-web php artisan route:cache
docker exec -it heaven-web php artisan view:cache
```

### 监控和维护
```bash
# 定期检查磁盘空间
df -h

# 清理Docker镜像
docker system prune -f

# 清理应用日志
find storage/logs -name "*.log" -mtime +30 -delete
```

---

**部署完成后，记得修改默认密码并配置SSL证书以确保安全！** 🔒
