# Heaven 项目

Heaven 是一个基于 Laravel 的 AI 视频生成平台，支持图生视频功能，集成了火山引擎等多种 AI 模型。

## 本地开发环境搭建

- `cp .env.example .env`
- `composer install`
- `php artisan key:generate`
- 删除数据库中所有表并重新运行所有迁移
  - `php artisan migrate:fresh --seed`
- 获取新的「省市县乡镇街道」数据
  - `php artisan pca:refreshData -d:file|jd`

## 服务器部署

### 环境要求
- Docker & Docker Compose
- PHP 8.1+
- MySQL 8.0+
- Redis
- Nginx

### 部署步骤

#### 1. 服务器信息
- **服务器IP**: `*************`
- **管理后台**: `http://*************:8081/admin`
- **H5前端**: `http://*************:8080`
- **API接口**: `http://*************:8081/api`

#### 2. 克隆项目
```bash
git clone <repository-url> heaven
cd heaven
```

#### 3. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑环境配置
vim .env
```

**重要配置项**：
```env
# 应用配置
APP_NAME=Heaven
APP_ENV=production
APP_DEBUG=false
APP_URL=http://*************:8081

# 数据库配置
DB_CONNECTION=mysql
DB_HOST=*************
DB_PORT=3306
DB_DATABASE=heaven
DB_USERNAME=yunyitang
DB_PASSWORD=your_password

# Redis配置
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

# 对象存储配置 (腾讯云COS)
COS_REGION=ap-beijing
COS_BUCKET=mk-1300059703
COS_APP_ID=1300059703
COS_SECRET_ID=your_secret_id
COS_SECRET_KEY=your_secret_key

# AI模型配置
VOLCANO_ENGINE_DOMAIN=https://ark.cn-beijing.volces.com/api/v3
VOLCANO_ENGINE_ACCESS_KEY=your_access_key
```

#### 4. Docker 部署
```bash
# 构建并启动容器
docker-compose -f docker-compose.richarvey.yml up -d

# 查看容器状态
docker-compose -f docker-compose.richarvey.yml ps

# 查看日志
docker-compose -f docker-compose.richarvey.yml logs -f heaven-web
```

#### 5. 应用初始化
```bash
# 进入容器
docker exec -it heaven-web bash

# 安装依赖
composer install --no-dev --optimize-autoloader

# 生成应用密钥
php artisan key:generate

# 运行数据库迁移
php artisan migrate --force

# 创建管理员账户
php artisan db:seed --class=AdminUserSeeder

# 清理缓存
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

#### 6. 定时任务配置
定时任务已自动配置，包括：
- 视频状态查询：每10分钟执行一次
- 自动插入评论：每30分钟和59分钟执行

查看定时任务状态：
```bash
docker exec -it heaven-web php artisan schedule:list
```

#### 7. 访问地址
- **管理后台**: http://*************:8081/admin
  - 默认账号：admin
  - 默认密码：admin
- **H5前端**: http://*************:8080
- **API文档**: http://*************:8081/api

#### 8. 部署脚本使用
项目提供了便捷的部署和管理脚本：

**部署脚本** (`deploy.sh`)：
```bash
# 查看帮助
./deploy.sh --help

# 完整部署
./deploy.sh production deploy

# 重启服务
./deploy.sh production restart

# 查看日志
./deploy.sh production logs

# 停止服务
./deploy.sh production stop
```

**状态检查脚本** (`status.sh`)：
```bash
# 检查项目整体状态
./status.sh
```

#### 9. 常用维护命令
```bash
# 查看容器状态
docker-compose -f docker-compose.richarvey.yml ps

# 重启服务
docker-compose -f docker-compose.richarvey.yml restart

# 查看日志
docker-compose -f docker-compose.richarvey.yml logs -f heaven-web

# 进入容器
docker exec -it heaven-web bash

# 手动执行视频状态查询
docker exec -it heaven-web php artisan video:query-status

# 清理缓存
docker exec -it heaven-web php artisan cache:clear
docker exec -it heaven-web php artisan config:clear
```

#### 10. 监控和日志
- **应用日志**: `storage/logs/`
- **视频处理日志**: `storage/logs/video/`
- **Nginx日志**: 通过 `docker logs heaven-web` 查看

#### 11. 故障排除

**定时任务不执行**：
```bash
# 检查定时任务配置
docker exec -it heaven-web php artisan schedule:list

# 手动运行调度器
docker exec -it heaven-web php artisan schedule:run

# 检查cron服务状态
docker exec -it heaven-web ps aux | grep cron

# 手动执行视频状态查询
docker exec -it heaven-web php artisan video:query-status
```

**视频生成失败**：
```bash
# 查看视频处理日志
docker exec -it heaven-web tail -f storage/logs/video/video-$(date +%Y-%m-%d).log

# 检查AI模型配置
docker exec -it heaven-web php artisan tinker
# 在tinker中执行：\App\AI\Models\AiModel::where('is_enabled', true)->get();
```

**容器启动失败**：
```bash
# 查看容器日志
docker-compose -f docker-compose.richarvey.yml logs heaven-web

# 重新构建容器
docker-compose -f docker-compose.richarvey.yml down
docker-compose -f docker-compose.richarvey.yml up -d --build
```

**数据库连接失败**：
```bash
# 检查数据库连接
docker exec -it heaven-web php artisan tinker
# 在tinker中执行：DB::connection()->getPdo();
```

### 功能特性
- 🎬 **AI视频生成**: 支持图生视频，集成火山引擎等多种AI模型
- 🎵 **背景音乐**: 支持视频背景音乐添加
- 📱 **H5前端**: 响应式移动端界面
- 🔧 **管理后台**: 完整的后台管理系统
- 📊 **数据统计**: 用户、视频、订单等数据统计
- 💰 **支付系统**: 集成支付功能
- 🔄 **定时任务**: 自动化视频状态查询和数据处理

### 技术栈
- **后端**: Laravel 9.x, PHP 8.1
- **前端**: Vue.js 2.x, Uni-app
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **容器**: Docker, Docker Compose
- **Web服务器**: Nginx
- **对象存储**: 腾讯云COS
- **AI服务**: 火山引擎、智谱清言、可灵AI

## 需要修改的地方
+ [ ] `app/Services/UserMoneyService.php:205` 需要传入"nickName"："会员昵称|姓名|身份证号"  ，按这个格式传输，开户时用户不用输入姓名和身份证号，且不支持修改，以前杉德不做验证现在不行
+ [ ] 需要增加更新居民遗像功能

## GIT 提交规范
- feat: 新功能（feature）
- fix: 修补bug
- docs: 文档（documentation）
- style: 格式（不影响代码运行的变动）
- refactor: 重构（即不是新增功能，也不是修改bug的代码变动）
- chore: 构建过程或辅助工具的变动
- revert: 撤销，版本回退
- perf: 性能优化
- test：测试
- improvement: 改进
- build: 打包
- ci: 持续集成

## 状态码

| 状态码 |              说明              |
|:---:|:----------------------------:|
| 200 |              正常              |
| 201 |             创建成功             |
| 202 |             接受请求             |
| 204 |             无内容              |
| 4xx |             请求错误             |
| 400 |            其他请求错误            |
| 401 |          未登陆需要重新登陆           |
| 403 |             禁止访问             |
| 404 |          未找到路由或没有数据          |
| 409 | 数据冲突（比如注册手机号已存在，实名认证身份证已存在等） |
| 429 |            请求过于频繁            |
| 5xx |            服务器错误             |
| 500 |           其他服务器错误            |
| 502 |             网关错误             |

## 通用请求方式

|  请求方式  |          说明          |
|:------:|:--------------------:|
|  GET   |         获取数据         |
|  POST  |         新建数据         |
|  PUT   | 更新数据 (整个对象，例如更新整篇文章) |
| PATCH  |  更新数据(部分对象,例如更新用户名)  |
| DELETE |         删除数据         |

> 针对不支持PATCH，PUT，DELETE的浏览器，可以使用POST，将_method放在请求中，如下：

```html

<form action="/example" method="POST">
    <input type="hidden" name="_method" value="PUT or PATCH or DELETE">
</form>
```

## 路由命名
对于资源的具体操作类型，由 HTTP 动词表示。常用的 HTTP 动词有下面五个（括号里是对应的 SQL 命令）。

- GET（SELECT）：从服务器取出资源（一项或多项）。
- POST（CREATE）：在服务器新建一个资源。
- PUT（UPDATE）：在服务器更新资源（客户端提供改变后的完整资源）。
- PATCH（UPDATE）：在服务器更新资源（客户端提供改变的属性）。
- DELETE（DELETE）：从服务器删除资源。

其中

1. 删除资源 必须 用 DELETE 方法
2. 创建新的资源 必须 使用 POST 方法
3. 更新资源 应该 使用 PUT 方法
4. 获取资源信息 必须 使用 GET 方法

针对每一个端点来说，下面列出所有可行的 HTTP 动词和端点的组合

| **请求方法** | **URL**                          | **描述**                       |
|----------|----------------------------------|------------------------------|
| GET      | /zoos                            | 列出所有的动物园 (ID 和名称，不要太详细)      |
| POST     | /zoos                            | 新增一个新的动物园                    |
| GET      | /zoos/{zoo}                      | 获取指定动物园详情                    |
| PUT      | /zoos/{zoo}                      | 更新指定动物园 (整个对象)               |
| PATCH    | /zoos/{zoo}                      | 更新动物园 (部分对象)                 |
| DELETE   | /zoos/{zoo}                      | 删除指定动物园                      |
| GET      | /zoos/{zoo}/animals              | 检索指定动物园下的动物列表 (ID 和名称，不要太详细) |
| GET      | /animals                         | 列出所有动物 (ID 和名称)。             |
| POST     | /animals                         | 新增新的动物                       |
| GET      | /animals/{animal}                | 获取指定的动物详情                    |
| PUT      | /animals/{animal}                | 更新指定的动物 (整个对象)               |
| PATCH    | /animals/{animal}                | 更新指定的动物 (部分对象)               |
| GET      | /animal_types                    | 获取所有动物类型 (ID 和名称，不要太详细)      |
| GET      | /animal_types/{type}             | 获取指定的动物类型详情                  |
| GET      | /employees                       | 检索整个雇员列表                     |
| GET      | /employees/{employee}            | 检索指定特定的员工                    |
| GET      | /zoos/{zoo}/employees            | 检索在这个动物园工作的雇员的名单 (身份证和姓名)    |
| POST     | /employees                       | 新增指定新员工                      |
| POST     | /zoos/{zoo}/employees            | 在特定的动物园雇佣一名员工                |
| DELETE   | /zoos/{zoo}/employees/{employee} | 从某个动物园解雇一名员工                 |

## 通用返回格式

失败时返回格式：

```json
{
  "status": "error",
  "code": 422,
  "msg": null,
  "data": null
}
```
成功时返回格式：
```json
{
  "status": "success",
  "code": 200,
  "msg": null,
  "data": null
}
```