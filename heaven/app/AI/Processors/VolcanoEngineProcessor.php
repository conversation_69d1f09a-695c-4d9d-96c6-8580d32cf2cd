<?php

namespace App\AI\Processors;

use App\AI\Models\AiModel;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * 火山引擎模型处理器
 */
class VolcanoEngineProcessor extends BaseModelProcessor
{
    /**
     * 创建视频生成任务
     *
     * @param string $imageUrl 输入图片的URL
     * @param string $prompt 提示词
     * @param array $options 其他选项
     * @return array API响应数据
     * @throws Exception
     */
    public function createVideoTask(?string $imageUrl, string $prompt, array $options = []): array
    {
        try {
            // 检查图片URL是否为空
            if (empty($imageUrl)) {
                throw new \InvalidArgumentException('图片URL不能为空');
            }

            // 检查图片URL是否是JSON字符串
            if (is_string($imageUrl) && strpos($imageUrl, '{') === 0) {
                // 尝试解析JSON
                $imageData = json_decode($imageUrl, true);
                if (json_last_error() === JSON_ERROR_NONE && isset($imageData['data']['url'])) {
                    // 使用COS URL
                    $imageUrl = $imageData['data']['url'];
                    Log::info('使用COS URL替换本地路径', [
                        'original' => $imageUrl,
                        'cos_url' => $imageData['data']['url']
                    ]);
                }
            }

            Log::channel('video')->info('使用火山引擎模型处理视频生成任务', [
                'model' => $this->config->model_name,
                'domain' => $this->config->domain,
                'image_url' => $imageUrl,
                'prompt' => $prompt
            ]);

            // 准备请求参数
            $params = [
                'model' => $this->config->ai_model_name,
                'content' => [
                    // 添加文本提示词
                    [
                        'type' => 'text',
                        'text' => $prompt . ($this->config->parameters ? ' ' . $this->config->parameters : '')
                    ],
                    // 添加图片URL
                    [
                        'type' => 'image_url',
                        'image_url' => [
                            'url' => $imageUrl
                        ]
                    ]
                ]
            ];

            // 添加随机种子参数
            if (isset($options['seed']) && $options['seed'] != -1) {
                $params['seed'] = $options['seed'];
            }

            // 构建正确的API URL
            $apiUrl = $this->config->domain;
            // 确保URL以/结尾
            $apiUrl = rtrim($apiUrl, '/') . '/';
            // 添加contents/generations/tasks路径
            $apiUrl .= 'contents/generations/tasks';


            // 调用API创建任务
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $this->config->access_key
            ])->post($apiUrl, $params);

            // 记录响应
            Log::info('火山引擎API响应', [
                'status' => $response->status(),
                'body' => $response->body(),
                'json' => $response->json(),
                'headers' => $response->headers()
            ]);



            if (!$response->successful()) {
                throw new Exception('火山引擎API请求失败: ' . $response->body());
            }

            $result = $response->json();

            // 标准化响应
            $standardized = $this->standardizeResponse($result);



            // 使用 Laravel 日志系统
            Log::channel('video')->info('火山引擎API响应数据', [
                'standardized' => $standardized
            ]);

            return $standardized;
        } catch (Exception $e) {
            Log::error('火山引擎API请求失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * 查询视频生成任务状态
     *
     * @param string $taskId 任务ID
     * @return array 包含任务状态信息的数组
     * @throws Exception
     */
    public function queryTaskStatus(string $taskId): array
    {
        try {
            Log::info('使用火山引擎模型查询任务状态', [
                'task_id' => $taskId
            ]);

            // 构建正确的API URL
            $apiUrl = $this->config->domain;
            // 确保URL以/结尾
            $apiUrl = rtrim($apiUrl, '/') . '/';
            // 添加contents/generations/tasks路径
            $apiUrl .= 'contents/generations/tasks/' . $taskId;

            // 记录请求URL
            Log::info('火山引擎查询任务状态URL', [
                'domain' => $this->config->domain,
                'constructed_url' => $apiUrl
            ]);

            // 调用API查询任务状态
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $this->config->access_key
            ])->get($apiUrl);

            if (!$response->successful()) {
                throw new Exception('火山引擎任务状态查询失败: ' . $response->body());
            }

            $result = $response->json();

            // 标准化响应
            $standardized = $this->standardizeResponse($result);

            Log::info('火山引擎任务状态查询响应', [
                'standardized' => $standardized
            ]);

            return $standardized;
        } catch (Exception $e) {
            Log::error('火山引擎任务状态查询失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * 标准化火山引擎API响应
     *
     * @param array $response 原始响应数据
     * @return array 标准化后的响应数据
     */
    protected function standardizeResponse(array $response): array
    {
        $standardized = [];

        // 记录原始响应以便调试
        Log::channel('video')->info('火山引擎原始响应', [
            'response' => $response,
            'response_type' => gettype($response),
            'response_keys' => is_array($response) ? array_keys($response) : 'not_array',
            'response_json' => json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
        ]);



        // 设置任务ID
        if (isset($response['id'])) {
            $standardized['task_id'] = $response['id'];
            Log::channel('video')->info('从响应中提取到任务ID: ' . $response['id']);


        } elseif (isset($response['task_id'])) {
            $standardized['task_id'] = $response['task_id'];
            Log::channel('video')->info('从响应中提取到任务ID: ' . $response['task_id']);


        } elseif (isset($response['data']) && isset($response['data']['id'])) {
            $standardized['task_id'] = $response['data']['id'];
            Log::channel('video')->info('从响应的data字段中提取到任务ID: ' . $response['data']['id']);


        } elseif (isset($response['data']) && isset($response['data']['task_id'])) {
            $standardized['task_id'] = $response['data']['task_id'];
            Log::channel('video')->info('从响应的data字段中提取到任务ID: ' . $response['data']['task_id']);


        } else {


            // 尝试从响应中查找任何可能的任务ID
            foreach ($response as $key => $value) {
                if (is_string($key) && (strpos($key, 'id') !== false || strpos($key, 'task') !== false)) {
                    Log::channel('video')->info('找到可能的任务ID字段: ' . $key . ' => ' . (is_string($value) ? $value : json_encode($value)));



                    // 如果找到id字段，使用它作为任务ID
                    if ($key === 'id' && (is_string($value) || is_numeric($value))) {
                        $standardized['task_id'] = (string)$value;
                        Log::channel('video')->info('使用找到的ID字段作为任务ID: ' . $value);



                        break;
                    }
                }

                if (is_array($value)) {
                    foreach ($value as $subKey => $subValue) {
                        if (is_string($subKey) && (strpos($subKey, 'id') !== false || strpos($subKey, 'task') !== false)) {
                            Log::channel('video')->info('在嵌套数组中找到可能的任务ID字段: ' . $key . '.' . $subKey . ' => ' . (is_string($subValue) ? $subValue : json_encode($subValue)));



                            // 如果找到id字段，使用它作为任务ID
                            if ($subKey === 'id' && (is_string($subValue) || is_numeric($subValue))) {
                                $standardized['task_id'] = (string)$subValue;
                                Log::channel('video')->info('使用嵌套数组中找到的ID字段作为任务ID: ' . $subValue);



                                break 2;
                            }
                        }
                    }
                }
            }

            // 如果仍然没有找到任务ID
            if (!isset($standardized['task_id'])) {
                Log::channel('video')->warning('无法从响应中提取任务ID', ['response' => $response]);



                // 如果实在找不到任务ID，使用当前时间戳作为临时任务ID
                $tempTaskId = 'temp_' . time() . '_' . rand(1000, 9999);
                $standardized['task_id'] = $tempTaskId;
                Log::channel('video')->warning('使用临时任务ID: ' . $tempTaskId);


            }


        }

        // 设置状态字段
        if (isset($response['status'])) {
            $status = $response['status'];
            // 处理中的状态都映射为"pending"
            if ($status == 'PROCESSING' || $status == 'PENDING' || $status == 'RUNNING' || $status == 'running') {
                $standardized['status'] = 'pending';
            }
            // 成功状态可能有多种表示方式
            elseif ($status == 'SUCCESS' || $status == 'COMPLETED' || $status == 'succeeded') {
                $standardized['status'] = 'success';
            }
            // 失败状态
            elseif ($status == 'FAIL' || $status == 'FAILED') {
                $standardized['status'] = 'failed';
            }
            else {
                // 其他未知状态，默认设置为"pending"
                Log::warning('火山引擎返回了未知状态: ' . $status . '，映射为pending');
                $standardized['status'] = 'pending';
            }
        }

        // 处理火山引擎特有的响应格式
        // 从 output 字段中提取视频URL
        if (isset($response['output']) && is_array($response['output'])) {
            $output = $response['output'];
            Log::info('找到 output 字段', ['output' => $output]);

            // 检查 output 是否包含视频URL
            if (isset($output['video_url'])) {
                $standardized['video_url'] = $output['video_url'];
                Log::info('从 output 字段中提取到视频URL: ' . $output['video_url']);
            }

            // 检查 output 是否为数组
            if (is_array($output) && !isset($output['video_url'])) {
                foreach ($output as $item) {
                    if (is_array($item) && isset($item['type']) && $item['type'] == 'video' && isset($item['url'])) {
                        $standardized['video_url'] = $item['url'];
                        Log::info('从 output 数组中提取到视频URL: ' . $item['url']);
                    }
                }
            }
        }

        // 从 content 字段中提取视频URL
        if (isset($response['content']) && is_array($response['content'])) {
            $content = $response['content'];
            Log::info('找到 content 字段', ['content' => $content]);

            if (isset($content['video_url'])) {
                $standardized['video_url'] = $content['video_url'];
                Log::info('从 content 字段中提取到视频URL: ' . $content['video_url']);
            }
        }

        // 处理结果数据
        if (isset($response['result']) && $response['result']) {
            $result = $response['result'];
            Log::info('找到 result 字段', ['result' => $result]);

            // 如果有视频URL，添加到标准响应中
            if (is_array($result) && count($result) > 0) {
                foreach ($result as $item) {
                    // 检查是否为视频类型
                    $isVideo = is_array($item) && isset($item['url']) && isset($item['type']) && $item['type'] == 'video';
                    if ($isVideo) {
                        $standardized['video_url'] = $item['url'];
                        Log::info('从 result 列表中提取到视频URL: ' . $item['url']);
                    }
                    // 检查是否为图片类型
                    $isImage = is_array($item) && isset($item['url']) && isset($item['type']) && $item['type'] == 'image';
                    if ($isImage) {
                        $standardized['cover_image_url'] = $item['url'];
                        Log::info('从 result 列表中提取到封面图URL: ' . $item['url']);
                    }
                }
            } elseif (is_array($result) && isset($result['url'])) {
                if (isset($result['type']) && $result['type'] == 'video') {
                    $standardized['video_url'] = $result['url'];
                    Log::info('从 result 字典中提取到视频URL: ' . $result['url']);
                } elseif (isset($result['type']) && $result['type'] == 'image') {
                    $standardized['cover_image_url'] = $result['url'];
                    Log::info('从 result 字典中提取到封面图URL: ' . $result['url']);
                }
            }
        }

        // 处理 choices 字段
        if (isset($response['choices']) && is_array($response['choices']) && !empty($response['choices'])) {
            Log::info('找到 choices 字段', ['choices' => $response['choices']]);

            foreach ($response['choices'] as $choice) {
                if (isset($choice['message']) && isset($choice['message']['content'])) {
                    $content = $choice['message']['content'];
                    Log::info('从 choices 字段中提取到内容', ['content' => $content]);

                    // 尝试解析 JSON 内容
                    $jsonContent = json_decode($content, true);
                    if ($jsonContent && isset($jsonContent['video_url'])) {
                        $standardized['video_url'] = $jsonContent['video_url'];
                        Log::info('从 choices 字段的 JSON 内容中提取到视频URL: ' . $jsonContent['video_url']);
                    }
                }
            }
        }

        // 处理错误信息
        if (isset($response['error']) && $response['error']) {
            $standardized['error'] = $response['error'];
            $standardized['status'] = 'failed';
        }

        // 保留原始响应
        $standardized['raw_response'] = $response;

        return $standardized;
    }
}
