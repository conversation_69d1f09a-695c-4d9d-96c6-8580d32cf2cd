<?php

namespace App\AI\Services;

use App\Models\Video;
use App\AI\Models\AiModel;
use App\AI\Services\ModelGeneratorFactory;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Exception;

class VideoGenerationService
{
    /**
     * 创建视频生成任务
     *
     * @param string $imageUrl 图片URL
     * @param string $prompt 提示词
     * @param int|null $customerId 客户ID
     * @return array 任务创建结果
     * @throws Exception
     */
    public function createVideoTask(string $imageUrl, string $prompt, ?int $customerId = null): array
    {


        try {
            // 确保使用video通道记录日志
            Log::channel('video')->info('开始创建视频生成任务', [
                'image_url' => $imageUrl,
                'prompt' => $prompt,
                'customer_id' => $customerId
            ]);

            // 同时写入到默认日志，以防video通道配置有问题
            Log::channel('video')->info('开始创建视频生成任务 [video]', [
                'image_url' => $imageUrl,
                'prompt' => $prompt,
                'customer_id' => $customerId
            ]);

            // 获取可用的AI模型
            $aiModel = $this->getAvailableAiModel();
            if (!$aiModel) {
                throw new Exception('没有可用的AI模型配置');
            }

            // 检查图片URL是否是相对路径，如果是，则转换为完整的COS URL
            $publicImageUrl = $this->uploadImageToCOS($imageUrl);
            Log::channel('video')->info('使用公开图片URL调用API', [
                'original_url' => $imageUrl,
                'public_url' => $publicImageUrl
            ]);

            // 调用AI模型API
            $response = $this->callModelApi($aiModel, $publicImageUrl, $prompt);

            // 在响应中添加公开图片URL，以便后续使用
            $response['public_image_url'] = $publicImageUrl;

            // 确保响应中包含task_id
            if (!isset($response['task_id'])) {
                throw new Exception('API响应缺少task_id: ' . json_encode($response));
            }

            // 检查是否已经存在相同task_id的记录
            $existingVideo = null;
            if (isset($response['task_id']) && $response['task_id']) {
                $existingVideo = \App\Models\Video::where('task_id', $response['task_id'])->first();
            }

            if ($existingVideo) {
                // 如果已经存在相同task_id的记录，使用现有记录
                Log::channel('video')->warning('发现已存在相同task_id的视频记录', [
                    'task_id' => $response['task_id'],
                    'existing_video_id' => $existingVideo->id
                ]);

                $video = $existingVideo;
            } else {
                // 检查是否已存在相同的图片URL和提示词的记录
                $existingVideo = \App\Models\Video::where('cover_image_url', $publicImageUrl)
                    ->where('prompt', $prompt)
                    ->where('status', 'pending')
                    ->first();

                if ($existingVideo) {
                    // 如果已存在相同的记录，使用现有记录
                    Log::channel('video')->warning('发现已存在相同的视频记录', [
                        'cover_image_url' => $publicImageUrl,
                        'prompt' => $prompt,
                        'existing_video_id' => $existingVideo->id
                    ]);

                    $video = $existingVideo;
                } else {
                    // 创建新的视频记录，使用公开图片URL
                    $video = $this->createVideoRecord($publicImageUrl, $prompt, $customerId);
                }

                // 更新视频记录，添加task_id和创建结果
                $this->updateVideoRecord($video, $response);
            }

            Log::channel('video')->info('视频生成任务创建成功', [
                'video_id' => $video->id,
                'task_id' => $video->task_id
            ]);

            return [
                'success' => true,
                'video_id' => $video->id,
                'task_id' => $video->task_id,
                'message' => '视频生成任务创建成功'
            ];
        } catch (Exception $e) {
            Log::channel('video')->error('视频生成任务创建失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * 将本地图片上传到COS并返回公开URL
     *
     * @param string $localImageUrl 本地图片URL
     * @return string 公开可访问的图片URL
     * @throws Exception
     */
    private function uploadImageToCOS(string $localImageUrl): string
    {
        try {
            // 检查图片URL是否是本地路径
            if (strpos($localImageUrl, 'http') === 0) {
                // 如果已经是HTTP URL，检查是否已经是COS URL
                if (strpos($localImageUrl, env('COS_CDN')) !== false) {
                    Log::channel('video')->info('图片已经是COS URL，无需上传', [
                        'image_url' => $localImageUrl
                    ]);
                    return $localImageUrl;
                }

                // 如果是其他HTTP URL，直接返回
                Log::channel('video')->info('图片是外部URL，无需上传', [
                    'image_url' => $localImageUrl
                ]);
                return $localImageUrl;
            }

            // 处理本地路径
            // 移除URL中的域名部分，只保留路径
            $localPath = parse_url($localImageUrl, PHP_URL_PATH);
            if (empty($localPath)) {
                $localPath = $localImageUrl;
            }

            // 移除开头的/storage，因为这是公共存储的符号链接
            $localPath = str_replace('/storage/', '', $localPath);

            // 检查文件是否存在于public磁盘
            if (!Storage::disk('public')->exists($localPath)) {
                throw new Exception('本地图片不存在: ' . $localPath);
            }

            // 读取文件内容
            $fileContent = Storage::disk('public')->get($localPath);

            // 生成COS路径
            $cosPath = 'videos/images/' . basename($localPath);

            // 上传到COS
            $uploaded = Storage::disk('cos')->put($cosPath, $fileContent, 'public');
            if (!$uploaded) {
                throw new Exception('上传图片到COS失败');
            }

            // 获取COS URL
            $cosUrl = Storage::disk('cos')->url($cosPath);

            Log::channel('video')->info('图片已上传到COS', [
                'local_path' => $localPath,
                'cos_path' => $cosPath,
                'cos_url' => $cosUrl
            ]);

            return $cosUrl;
        } catch (Exception $e) {
            Log::channel('video')->error('上传图片到COS失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * 获取可用的AI模型
     *
     * @return AiModel|null
     */
    private function getAvailableAiModel(): ?AiModel
    {
        return AiModel::enabled()->first();
    }

    /**
     * 创建视频记录
     *
     * @param string $imageUrl 图片URL
     * @param string $prompt 提示词
     * @param int|null $customerId 客户ID
     * @return Video
     */
    private function createVideoRecord(string $imageUrl, string $prompt, ?int $customerId): Video
    {
        $video = new Video();
        $video->cover_image_url = $imageUrl;
        $video->prompt = $prompt;
        $video->status = 'pending';
        $video->is_active = true;
        $video->customer_id = $customerId;
        $video->bot_image_check = 1;
        $video->p_image_check = 1;
        $video->bot_video_check = 1;
        $video->p_video_check = 1;
        $video->save();

        return $video;
    }

    /**
     * 调用AI模型API
     *
     * @param AiModel $aiModel AI模型配置
     * @param string $imageUrl 图片URL
     * @param string $prompt 提示词
     * @return array API响应
     * @throws Exception
     */
    private function callModelApi(AiModel $aiModel, string $imageUrl, string $prompt): array
    {
        try {
            // 使用模型生成器工厂
            $factory = new ModelGeneratorFactory();

            // 创建视频任务
            $result = $factory->createVideoTask($imageUrl, $prompt);

            // 直接返回结果，外部会检查task_id是否存在
            return $result;
        } catch (Exception $e) {
            Log::channel('video')->error('调用AI模型API失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * 更新视频记录
     *
     * @param Video $video 视频记录
     * @param array $response API响应
     * @return void
     */
    private function updateVideoRecord(Video $video, array $response): void
    {


        // 同时记录到video日志通道
        Log::channel('video')->info('更新视频记录', [
            'video_id' => $video->id,
            'task_id' => $response['task_id'] ?? null,
            'response' => $response
        ]);

        // 检查响应中是否有task_id
        if (!isset($response['task_id'])) {
            Log::channel('video')->warning('响应中缺少task_id', [
                'response' => $response
            ]);

            // 尝试从响应中查找任何可能的任务ID
            foreach ($response as $key => $value) {
                if (is_string($key) && (strpos($key, 'id') !== false || strpos($key, 'task') !== false)) {
                    Log::channel('video')->info('找到可能的任务ID字段', [
                        'field' => $key,
                        'value' => $value
                    ]);

                    // 如果找到id字段，使用它作为任务ID
                    if ($key === 'id' && (is_string($value) || is_numeric($value))) {
                        $response['task_id'] = (string)$value;
                        Log::channel('video')->info('使用找到的ID字段作为任务ID', [
                            'task_id' => $value
                        ]);
                        break;
                    }
                }
            }
        }

        // 更新视频记录
        $video->task_id = $response['task_id'] ?? null;
        $video->create_result = json_encode($response);
        $video->save();

        Log::channel('video')->info('视频记录已更新', [
            'video_id' => $video->id,
            'task_id' => $video->task_id ?? '未设置'
        ]);
    }

    /**
     * 处理视频生成回调
     *
     * @param array $callbackData 回调数据
     * @return array 处理结果
     * @throws Exception
     */
    public function handleCallback(array $callbackData): array
    {
        try {
            Log::channel('video')->info('收到视频生成回调', [
                'data' => $callbackData
            ]);

            // 获取任务ID
            $taskId = $callbackData['task_id'] ?? null;
            if (!$taskId) {
                throw new Exception('回调数据缺少task_id');
            }

            // 查找对应的视频记录
            $video = Video::where('task_id', $taskId)->first();
            if (!$video) {
                throw new Exception('未找到对应的视频记录: ' . $taskId);
            }

            // 更新视频记录
            $video->task_result = json_encode($callbackData);

            // 如果回调包含视频URL，则更新视频URL
            if (isset($callbackData['video_url'])) {
                $video->video_url = $callbackData['video_url'];
                $video->status = 'success';
            } elseif (isset($callbackData['status']) && $callbackData['status'] === 'failed') {
                $video->status = 'failed';
            }

            $video->save();

            Log::channel('video')->info('视频生成回调处理成功', [
                'video_id' => $video->id,
                'status' => $video->status
            ]);

            return [
                'success' => true,
                'video_id' => $video->id,
                'status' => $video->status,
                'message' => '回调处理成功'
            ];
        } catch (Exception $e) {
            Log::channel('video')->error('视频生成回调处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * 查询视频生成状态
     *
     * @param int $videoId 视频ID
     * @return array 查询结果
     * @throws Exception
     */
    public function queryVideoStatus(int $videoId): array
    {
        try {
            $video = Video::findOrFail($videoId);

            // 如果视频已经生成成功或失败，直接返回状态
            if ($video->status === 'success' || $video->status === 'failed') {
                return [
                    'success' => true,
                    'video_id' => $video->id,
                    'status' => $video->status,
                    'video_url' => $video->video_url,
                    'message' => '视频状态: ' . $video->status
                ];
            }

            // 如果没有任务ID，无法查询
            if (!$video->task_id) {
                throw new Exception('视频记录缺少task_id');
            }

            // 获取AI模型配置
            $aiModel = $this->getAvailableAiModel();
            if (!$aiModel) {
                throw new Exception('没有可用的AI模型配置');
            }

            // 使用模型生成器工厂
            $factory = new ModelGeneratorFactory();

            // 查询任务状态
            $result = $factory->queryTaskStatus($video->task_id);

            // 更新视频记录
            $video->task_result = json_encode($result);

            // 如果任务完成，更新视频URL和状态
            if (isset($result['status'])) {
                if ($result['status'] === 'success' && isset($result['video_url'])) {
                    $video->video_url = $result['video_url'];
                    $video->status = 'success';
                } elseif ($result['status'] === 'failed') {
                    $video->status = 'failed';
                }
            }

            $video->save();

            return [
                'success' => true,
                'video_id' => $video->id,
                'status' => $video->status,
                'video_url' => $video->video_url,
                'message' => '视频状态已更新'
            ];
        } catch (Exception $e) {
            Log::channel('video')->error('查询视频状态失败', [
                'video_id' => $videoId,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }
}
