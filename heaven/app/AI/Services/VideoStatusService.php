<?php

namespace App\AI\Services;

use App\Models\Video;
use Illuminate\Support\Facades\Log;
use Exception;

class VideoStatusService
{
    /**
     * 批量查询视频状态
     *
     * @param int $limit 每次查询的数量限制
     * @return array 查询结果统计
     */
    public function batchQueryStatus(int $limit = 50): array
    {
        $stats = [
            'total' => 0,
            'success' => 0,
            'failed' => 0,
            'pending' => 0,
            'error' => 0
        ];

        try {
            Log::channel('video')->info("开始批量查询视频状态", ['limit' => $limit]);

            // 获取所有状态为 pending 的视频，限制数量
            // 按创建时间排序，优先处理较早创建的视频
            $pendingVideos = Video::where('status', 'pending')
                ->orderBy('created_at', 'asc')
                ->limit($limit)
                ->get();

            $stats['total'] = $pendingVideos->count();

            Log::channel('video')->info("找到待处理视频", [
                'count' => $stats['total'],
                'video_ids' => $pendingVideos->pluck('id')->toArray()
            ]);

            if ($pendingVideos->isEmpty()) {
                Log::channel('video')->info("没有待处理视频，批量查询结束");
                return $stats;
            }

            // 创建视频生成服务实例
            $videoGenerationService = new VideoGenerationService();

            // 逐个处理每个视频
            foreach ($pendingVideos as $video) {
                try {
                    Log::channel('video')->info("开始查询视频状态", [
                        'video_id' => $video->id,
                        'task_id' => $video->task_id
                    ]);

                    // 使用单条查询方法查询视频状态
                    $result = $this->querySingleVideoStatus($video->id, $videoGenerationService);

                    // 根据结果更新统计信息
                    if ($result['status'] === 'success') {
                        $stats['success']++;
                        Log::channel('video')->info("视频生成成功", [
                            'video_id' => $video->id,
                            'video_url' => $result['video_url'] ?? '未知'
                        ]);
                    } elseif ($result['status'] === 'failed') {
                        $stats['failed']++;
                        Log::channel('video')->info("视频生成失败", [
                            'video_id' => $video->id
                        ]);
                    } else {
                        $stats['pending']++;
                        Log::channel('video')->info("视频仍在处理中", [
                            'video_id' => $video->id
                        ]);
                    }
                } catch (Exception $e) {
                    $stats['error']++;
                    Log::channel('video')->error("查询视频状态出错", [
                        'video_id' => $video->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);

                    // 如果是网络错误或超时，增加延迟时间
                    if (strpos($e->getMessage(), 'cURL error') !== false ||
                        strpos($e->getMessage(), 'timeout') !== false) {
                        Log::channel('video')->error("检测到网络错误或超时，增加延迟时间");
                        usleep(500000); // 500毫秒
                    }
                }

                // 添加短暂延迟，避免API请求过于频繁
                usleep(200000); // 200毫秒
            }

            // 检查是否还有更多待处理视频
            $remainingCount = Video::where('status', 'pending')->count();

            Log::channel('video')->info("批量查询视频状态完成", [
                'total' => $stats['total'],
                'success' => $stats['success'],
                'failed' => $stats['failed'],
                'pending' => $stats['pending'],
                'error' => $stats['error'],
                'remaining' => $remainingCount
            ]);

            return $stats;
        } catch (Exception $e) {
            Log::channel('video')->error("批量查询视频状态出错", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * 查询单个视频状态
     *
     * @param int $videoId 视频ID
     * @param VideoGenerationService|null $service 视频生成服务实例
     * @return array 查询结果
     * @throws Exception
     */
    private function querySingleVideoStatus(int $videoId, ?VideoGenerationService $service = null): array
    {
        try {
            // 如果没有传入服务实例，创建一个新的
            if (!$service) {
                $service = new VideoGenerationService();
            }

            // 查询视频状态
            $result = $service->queryVideoStatus($videoId);

            // 记录查询结果
            Log::channel('video')->info("单个视频状态查询结果", [
                'video_id' => $videoId,
                'status' => $result['status'] ?? 'unknown',
                'video_url' => $result['video_url'] ?? null
            ]);

            return $result;
        } catch (Exception $e) {
            Log::channel('video')->error("查询单个视频状态失败", [
                'video_id' => $videoId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * 获取待处理视频的数量
     *
     * @return int 待处理视频的数量
     */
    public function getPendingCount(): int
    {
        try {
            // 记录开始查询
            Log::channel('video')->info('开始查询待处理视频数量');

            // 获取数据库连接信息
            $connection = config('database.default');
            $host = config("database.connections.{$connection}.host");
            $database = config("database.connections.{$connection}.database");
            $username = config("database.connections.{$connection}.username");

            Log::channel('video')->info('数据库连接信息', [
                'connection' => $connection,
                'host' => $host,
                'database' => $database,
                'username' => $username
            ]);

            // 测试数据库连接
            try {
                $testResult = \DB::select('SELECT 1 as test');
                Log::channel('video')->info('数据库连接测试成功', ['result' => $testResult]);
            } catch (\Exception $dbError) {
                Log::channel('video')->error('数据库连接测试失败', [
                    'error' => $dbError->getMessage(),
                    'trace' => $dbError->getTraceAsString()
                ]);

                // 尝试使用原始查询
                Log::channel('video')->error('尝试使用原始查询');
                $pdo = new \PDO(
                    "mysql:host={$host};dbname={$database}",
                    $username,
                    config("database.connections.{$connection}.password")
                );
                $stmt = $pdo->query('SELECT COUNT(*) FROM videos WHERE status = "pending"');
                $count = $stmt->fetchColumn();
                Log::channel('video')->error('原始查询成功', ['count' => $count]);
                return (int)$count;
            }

            // 查询待处理视频数量
            Log::channel('video')->info('开始查询视频表');
            $totalVideos = Video::count();
            Log::channel('video')->info('视频总数', ['count' => $totalVideos]);

            // 查询各种状态的视频数量
            $pendingCount = Video::where('status', 'pending')->count();
            $successCount = Video::where('status', 'success')->count();
            $failedCount = Video::where('status', 'failed')->count();

            Log::channel('video')->info('各状态视频数量', [
                'pending' => $pendingCount,
                'success' => $successCount,
                'failed' => $failedCount
            ]);

            // 查询待处理视频数量（包括有任务ID和没有任务ID的）
            $count = Video::where('status', 'pending')->count();

            Log::channel('video')->info('查询到待处理视频数量', [
                'total_pending' => $count
            ]);

            return $count;
        } catch (\Exception $e) {
            Log::channel('video')->error('查询待处理视频数量失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 出错时返回0，避免后续处理出错
            return 0;
        }
    }

    /**
     * 获取视频状态统计
     *
     * @return array 视频状态统计
     */
    public function getStatusStats(): array
    {
        $stats = [
            'total' => Video::count(),
            'pending' => Video::where('status', 'pending')->count(),
            'success' => Video::where('status', 'success')->count(),
            'failed' => Video::where('status', 'failed')->count(),
            'active' => Video::where('is_active', true)->count(),
            'inactive' => Video::where('is_active', false)->count()
        ];

        return $stats;
    }


}
