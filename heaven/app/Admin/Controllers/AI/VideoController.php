<?php

namespace App\Admin\Controllers\AI;

use App\Admin\Repositories\AI\Video;
use App\Enums\FilePrefix;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Widgets\Modal;
use Dcat\Admin\Widgets\Card;
use Dcat\Admin\Widgets\Form as WidgetForm;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\AI\Services\VideoGenerationService;
use App\AI\Services\VideoStatusService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Artisan;
use App\Models\ArtisanScheduleTask;

class VideoController extends AdminController
{
    /**
     * 调试日志文件路径
     *
     * @var string
     */
    protected $debugLogFile;
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Video(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('cover_image_url', '封面图片')->image('', 100, 60);
            $grid->column('video_url', '视频')->display(function ($video_url) {
                if (empty($video_url)) return '';
                return "<video src='{$video_url}' controls style='max-width:200px;max-height:120px;'></video>";
            });
            $grid->column('prompt', '提示词')->limit(20);
            $grid->column('status', '生成状态')->using([
                'pending' => '处理中',
                'success' => '成功',
                'failed' => '失败'
            ])->label([
                'pending' => 'primary',
                'success' => 'success',
                'failed' => 'danger',
            ]);
            $grid->column('is_active', '可用状态')->switch();

            // 添加审核状态列
            $grid->column('bot_image_check', '机器图片审核')->display(function ($value) {
                return $value == 1
                    ? '<span class="badge bg-success"><i class="feather icon-check"></i></span>'
                    : '<span class="badge bg-danger"><i class="feather icon-x"></i></span>';
            });

            $grid->column('p_image_check', '人工图片审核')->display(function ($value) {
                return $value == 1
                    ? '<span class="badge bg-success"><i class="feather icon-check"></i></span>'
                    : '<span class="badge bg-danger"><i class="feather icon-x"></i></span>';
            });

            $grid->column('bot_video_check', '机器视频审核')->display(function ($value) {
                return $value == 1
                    ? '<span class="badge bg-success"><i class="feather icon-check"></i></span>'
                    : '<span class="badge bg-danger"><i class="feather icon-x"></i></span>';
            });

            $grid->column('p_video_check', '人工视频审核')->display(function ($value) {
                return $value == 1
                    ? '<span class="badge bg-success"><i class="feather icon-check"></i></span>'
                    : '<span class="badge bg-danger"><i class="feather icon-x"></i></span>';
            });

            $grid->column('check_remark', '审核备注')->limit(15);
            $grid->column('created_at', '创建时间');

            // 禁用默认操作列
            $grid->disableActions();

            // 添加自定义操作列
            $grid->column('操作')->display(function () {
                $reviewButton = '<a href="javascript:void(0);" class="btn btn-sm btn-primary"
                    data-id="'.$this->id.'"
                    onclick="openReviewModal('.$this->id.')">
                    <i class="feather icon-check-square"></i> 审核
                </a>';

                $viewButton = '<a href="javascript:void(0);" class="btn btn-sm btn-info"
                    data-id="'.$this->id.'"
                    onclick="openViewModal('.$this->id.')">
                    <i class="feather icon-eye"></i> 详情
                </a>';

                $queryButton = '<a href="javascript:void(0);" class="btn btn-sm btn-warning"
                    data-id="'.$this->id.'"
                    onclick="queryVideoStatus('.$this->id.')">
                    <i class="feather icon-refresh-cw"></i> 查询状态
                </a>';

                return $reviewButton . '&nbsp;' . $viewButton . '&nbsp;' . $queryButton;
            });

            $grid->showColumnSelector(); // 开启字段选择器功能
            $grid->paginate(10);
            $grid->model()->orderBy('id', 'desc');
            $grid->quickSearch(['prompt', 'id'])->placeholder('搜索ID/提示词'); //快捷搜索

            // 禁用默认的创建按钮
            $grid->disableCreateButton();

            // 添加自定义按钮
            $grid->tools(function (Grid\Tools $tools) {
                $tools->append('<a href="javascript:void(0);" class="btn btn-primary" onclick="openCreateModal()"><i class="feather icon-plus"></i> 创建视频</a>');
                $tools->append('&nbsp;<a href="javascript:void(0);" class="btn btn-warning" onclick="batchQueryStatus()"><i class="feather icon-refresh-cw"></i> 批量查询状态</a>');
                $tools->append('&nbsp;<a href="javascript:void(0);" class="btn btn-success" onclick="createTestVideos()"><i class="feather icon-plus"></i> 创建测试视频</a>');
                $tools->append('&nbsp;<a href="javascript:void(0);" class="btn btn-info" onclick="manageScheduleTask()"><i class="feather icon-clock"></i> 管理定时任务</a>');
            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id', 'ID')->width(3);
                $filter->like('prompt', '提示词')->width(3);
                $filter->equal('status', '状态')->select([
                    'pending' => '处理中',
                    'success' => '成功',
                    'failed' => '失败'
                ])->width(3);
                $filter->equal('is_active', '是否启用')->select([0 => '禁用', 1 => '启用'])->width(3);
                $filter->equal('bot_image_check', '机器图片审核')->select([0 => '未通过', 1 => '通过'])->width(3);
                $filter->equal('p_image_check', '人工图片审核')->select([0 => '未通过', 1 => '通过'])->width(3);
                $filter->equal('bot_video_check', '机器视频审核')->select([0 => '未通过', 1 => '通过'])->width(3);
                $filter->equal('p_video_check', '人工视频审核')->select([0 => '未通过', 1 => '通过'])->width(3);
                $filter->between('created_at', '创建时间')->datetime()->width(3);
            });

            // 添加JS脚本
            $grid->header(function () {
                return $this->renderScripts();
            });
        });
    }

    /**
     * 渲染JS脚本
     */
    protected function renderScripts()
    {
        // 使用 JavaScript 原生方法，避免使用 PHP 的 heredoc 语法
        $script = '<script>';

        // 打开审核模态框
        $script .= '
// 打开审核模态框
function openReviewModal(id) {
    // 使用Dcat Admin的Dialog
    Dcat.confirm("确定要审核此视频吗？", "", function () {
        // 跳转到审核页面
        window.location.href = "/admin/ai/video/" + id + "/review";
    });
}
        ';

        // 打开查看详情模态框
        $script .= '
// 打开查看详情模态框
function openViewModal(id) {
    // 跳转到详情页面
    window.location.href = "/admin/ai/video/" + id;
}
        ';

        // 查询视频状态
        $script .= '
// 查询视频状态
function queryVideoStatus(id) {
    // 使用Dcat Admin的Dialog
    Dcat.confirm("确定要查询此视频的状态吗？", "", function () {
        // 显示加载中提示
        var loadingId = Dcat.loading();

        // 使用AJAX请求查询状态
        $.ajax({
            url: "' . admin_url('ai/video') . '/" + id + "/query-status-ajax",
            type: "GET",
            success: function(response) {
                // 关闭加载提示
                Dcat.loading(loadingId);

                // 显示成功消息
                if (response.status) {
                    Dcat.success(response.message);
                } else {
                    Dcat.error(response.message);
                }

                // 刷新当前页面
                setTimeout(function() {
                    Dcat.reload();
                }, 1500);
            },
            error: function(xhr) {
                // 关闭加载提示
                Dcat.loading(loadingId);

                // 显示错误消息
                Dcat.error("查询失败: " + (xhr.responseJSON ? xhr.responseJSON.message : "未知错误"));
                console.error("查询失败", xhr);
            }
        });
    });
}
        ';

        // 打开创建视频模态框
        $script .= '
// 打开创建视频模态框
function openCreateModal() {
    // 跳转到创建页面
    window.location.href = "/admin/ai/video/create";
}
        ';

        // 获取批量查询状态的URL
        $batchQueryStatusUrl = admin_url('ai/batch-query-video-status');

        // 记录URL到日志，方便调试
        Log::channel('video')->info('批量查询状态URL', [
            'url' => $batchQueryStatusUrl
        ]);

        // 批量查询视频状态
        $script .= '
// 批量查询视频状态
function batchQueryStatus() {
    // 使用Dcat Admin的Dialog
    Dcat.confirm("确定要批量查询视频状态吗？", "系统将在后台处理，您可以继续其他操作", function () {
        // 显示简短的成功提示，不阻塞界面
        Dcat.success("批量查询已开始，系统将在后台处理");

        // 使用AJAX请求批量查询状态，但不等待结果
        $.ajax({
            url: "' . $batchQueryStatusUrl . '", // 使用动态生成的URL
            type: "GET",
            timeout: 300000, // 5分钟超时，允许长时间运行
            success: function(response) {
                console.log("批量查询完成:", response);

                // 只在控制台显示结果，不弹出对话框
                if (response.status) {
                    console.log("查询结果统计:", {
                        "总数": response.data.total,
                        "成功": response.data.success,
                        "失败": response.data.failed,
                        "处理中": response.data.pending,
                        "错误": response.data.error
                    });

                    // 静默刷新页面，不打断用户操作
                    setTimeout(function() {
                        // 使用fetch API静默刷新数据
                        fetch(window.location.href)
                            .then(response => response.text())
                            .then(html => {
                                // 提取表格内容并更新
                                var parser = new DOMParser();
                                var doc = parser.parseFromString(html, "text/html");
                                var newTable = doc.querySelector(".table-responsive");
                                if (newTable) {
                                    var currentTable = document.querySelector(".table-responsive");
                                    if (currentTable) {
                                        currentTable.innerHTML = newTable.innerHTML;
                                    }
                                }
                            })
                            .catch(error => {
                                console.error("静默刷新失败:", error);
                            });
                    }, 5000); // 5秒后静默刷新
                } else {
                    console.error("批量查询返回错误:", response.message);
                }
            },
            error: function(xhr) {
                console.error("批量查询请求失败:", xhr);

                // 记录错误详情到控制台，方便调试
                console.log("请求URL:", "' . $batchQueryStatusUrl . '");
                console.log("状态码:", xhr.status);
                console.log("状态文本:", xhr.statusText);
                console.log("响应文本:", xhr.responseText);
            }
        });
    });
}
        ';

        // 管理定时任务
        $script .= '
// 管理定时任务
function manageScheduleTask() {
    // 跳转到定时任务管理页面
    window.location.href = "/admin/ai/video/manage-schedule-task";
}
        ';

        // 添加创建测试视频的函数
        $script .= '
// 创建测试视频
function createTestVideos() {
    // 使用Dcat Admin的Dialog
    Dcat.confirm("确定要创建测试视频吗？", "系统将创建5个测试视频用于测试批量查询功能", function () {
        // 显示简短的成功提示，不阻塞界面
        Dcat.success("开始创建测试视频，请稍候...");

        // 使用AJAX请求创建测试视频
        $.ajax({
            url: "' . admin_url('ai/create-test-videos') . '",
            type: "GET",
            data: {
                count: 5 // 默认创建5个测试视频
            },
            success: function(response) {
                console.log("创建测试视频完成:", response);

                if (response.status) {
                    Dcat.success("成功创建 " + response.data.count + " 个测试视频");

                    // 静默刷新页面
                    setTimeout(function() {
                        Dcat.reload();
                    }, 2000);
                } else {
                    Dcat.error(response.message);
                }
            },
            error: function(xhr) {
                console.error("创建测试视频失败:", xhr);
                Dcat.error("创建测试视频失败: " + (xhr.responseJSON ? xhr.responseJSON.message : "未知错误"));
            }
        });
    });
}
        ';

        // 添加修复模态框的脚本
        $script .= '
// 检测并修复模态框问题
$(document).ready(function() {
    // 检查是否有残留的模态框背景
    if ($(".modal-backdrop").length > 0) {
        $(".modal-backdrop").remove();
        $("body").removeClass("modal-open").css("padding-right", "");
        console.log("检测到残留的模态框背景，已移除");
    }

    // 监听ESC键，关闭所有模态框
    $(document).on("keydown", function(e) {
        if (e.keyCode === 27) { // ESC键
            $(".modal-backdrop").remove();
            $("body").removeClass("modal-open").css("padding-right", "");
            console.log("按下ESC键，关闭所有模态框");
        }
    });
});
        ';

        $script .= '</script>';

        return $script;
    }

    /**
     * 视频审核页面
     */
    public function review($id, Content $content)
    {
        $video = \App\Models\Video::findOrFail($id);

        $form = new WidgetForm();
        $form->title('视频审核');

        // 显示视频信息
        $form->display('id', 'ID')->value($video->id);

        if ($video->cover_image_url) {
            $form->display('cover_image_url', '封面图片')->with(function () use ($video) {
                return "<img src='{$video->cover_image_url}' style='max-width:300px;max-height:200px;'>";
            });
        }

        if ($video->video_url) {
            $form->display('video_url', '视频')->with(function () use ($video) {
                return "<video src='{$video->video_url}' controls style='max-width:400px;max-height:300px;'></video>";
            });
        }

        $form->display('prompt', '提示词')->value($video->prompt);
        $form->display('status', '状态')->value($this->getStatusText($video->status));

        // 审核选项
        $form->switch('bot_image_check', '机器图片审核')->default($video->bot_image_check);
        $form->switch('p_image_check', '人工图片审核')->default($video->p_image_check);
        $form->switch('bot_video_check', '机器视频审核')->default($video->bot_video_check);
        $form->switch('p_video_check', '人工视频审核')->default($video->p_video_check);
        $form->switch('is_active', '是否启用')->default($video->is_active);
        $form->textarea('check_remark', '审核备注')->value($video->check_remark)->rows(3);

        $form->action(admin_url('ai/video/'.$id.'/review'));
        $form->method('POST');

        // 添加CSRF令牌
        $form->hidden('_token')->default(csrf_token());

        return $content
            ->title('视频审核')
            ->description('审核视频内容')
            ->body($form);
    }

    /**
     * 处理审核提交
     */
    public function reviewStore(Request $request, $id)
    {
        $video = \App\Models\Video::findOrFail($id);

        // 更新审核状态
        $video->bot_image_check = $request->input('bot_image_check', 0);
        $video->p_image_check = $request->input('p_image_check', 0);
        $video->bot_video_check = $request->input('bot_video_check', 0);
        $video->p_video_check = $request->input('p_video_check', 0);
        $video->is_active = $request->input('is_active', 0);
        $video->check_remark = $request->input('check_remark');

        $video->save();

        admin_success('审核成功', '视频审核状态已更新');

        return redirect()->route('admin.ai.video.index');
    }

    /**
     * 获取状态文本
     */
    protected function getStatusText($status)
    {
        $statusMap = [
            'pending' => '处理中',
            'success' => '成功',
            'failed' => '失败'
        ];

        return $statusMap[$status] ?? $status;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Video(), function (Show $show) {
            $show->field('id');
            $show->field('creator_id', '创建人ID');
            $show->field('cover_image_url', '封面图片')->image();
            $show->field('video_url', '视频')->unescape()->as(function ($video_url) {
                if (empty($video_url)) return '';
                return "<video src='{$video_url}' controls style='max-width:400px;max-height:300px;'></video>";
            });
            $show->field('prompt', '提示词');
            $show->field('status', '状态')->using([
                'pending' => '处理中',
                'success' => '成功',
                'failed' => '失败'
            ]);
            $show->field('is_active', '是否启用')->using([0 => '禁用', 1 => '启用']);
            $show->field('task_id', '任务ID');
            $show->field('create_result', '创建结果')->unescape()->as(function ($value) {
                return '<pre style="max-height:300px;overflow:auto;">' . json_encode(json_decode($value), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
            });
            $show->field('task_result', '任务结果')->unescape()->as(function ($value) {
                return '<pre style="max-height:300px;overflow:auto;">' . json_encode(json_decode($value), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
            });
            $show->field('customer_id', '客户ID');
            $show->field('bot_image_check', '机器图片验证')->using([0 => '未通过', 1 => '通过']);
            $show->field('p_image_check', '人工图片验证')->using([0 => '未通过', 1 => '通过']);
            $show->field('bot_video_check', '机器视频验证')->using([0 => '未通过', 1 => '通过']);
            $show->field('p_video_check', '人工视频验证')->using([0 => '未通过', 1 => '通过']);
            $show->field('check_remark', '验证信息');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');

            // 添加返回按钮
            $show->tools(function (Show\Tools $tools) {
                $tools->append('<a href="' . admin_url('ai/video') . '" class="btn btn-sm btn-white"><i class="feather icon-arrow-left"></i> 返回列表</a>');
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Video(), function (Form $form) {
            // 判断是否是编辑模式
            $isEditing = $form->isEditing();

            if ($isEditing) {
                $form->display('id');
            }

            // 封面图片 - 必填
            $form->image('cover_image_url', '封面图片')
                ->move(FilePrefix::VIDEO->getFilePath())
                ->removable(false)
                ->accept('jpg,png,gif,jpeg')
                ->uniqueName()
                ->autoUpload()
                ->disk('cos') // 使用腾讯云COS存储
                ->saveFullUrl(true) // 保存完整URL而不是路径
                ->required()
                ->help('上传图片作为视频的封面和生成素材');

            // 提示词 - 必填
            $form->text('prompt', '提示词')
                ->required()
                ->help('输入提示词，用于指导AI生成视频');

            // 只在编辑模式下显示其他字段
            if ($isEditing) {
                $form->url('video_url', '视频URL');
                $form->select('status', '状态')->options([
                    'pending' => '处理中',
                    'success' => '成功',
                    'failed' => '失败'
                ])->default('pending');
                $form->switch('is_active', '是否启用')->default(true);
                $form->text('task_id', '任务ID');
                $form->textarea('create_result', '创建结果');
                $form->textarea('task_result', '任务结果');
                $form->number('customer_id', '客户ID');
                $form->switch('bot_image_check', '机器图片验证')->default(1);
                $form->switch('p_image_check', '人工图片验证')->default(1);
                $form->switch('bot_video_check', '机器视频验证')->default(1);
                $form->switch('p_video_check', '人工视频验证')->default(1);
                $form->text('check_remark', '验证信息');
                $form->display('created_at', '创建时间');
                $form->display('updated_at', '更新时间');
            } else {
                // 在创建模式下添加说明
                $form->divider('视频生成说明');
                $form->html('
                    <div class="alert alert-info">
                        <p><i class="feather icon-info"></i> 提交后系统将自动调用AI模型生成视频，请耐心等待。</p>
                        <p>生成过程可能需要几分钟时间，您可以在列表页查看生成状态。</p>
                    </div>
                ');
            }

            // 保存前回调
            $form->saving(function (Form $form) {
                // 如果是新建视频，设置默认值
                if (!$form->isEditing()) {
                    $form->status = 'pending';
                    $form->is_active = 1;
                    $form->bot_image_check = 1;
                    $form->p_image_check = 1;
                    $form->bot_video_check = 1;
                    $form->p_video_check = 1;

                    // 检查封面图片URL是否是上传后的JSON响应
                    $coverImageUrl = $form->cover_image_url;
                    if (is_string($coverImageUrl) && strpos($coverImageUrl, '{') === 0) {
                        // 尝试解析JSON
                        $imageData = json_decode($coverImageUrl, true);
                        if (json_last_error() === JSON_ERROR_NONE && isset($imageData['data']['url'])) {
                            // 使用COS URL
                            $form->cover_image_url = $imageData['data']['url'];
                            \Illuminate\Support\Facades\Log::info('使用COS URL替换本地路径', [
                                'original' => $coverImageUrl,
                                'cos_url' => $imageData['data']['url']
                            ]);
                        }
                    }
                }
            });

            // 表单提交成功后的回调
            $form->saved(function (Form $form) {
                // 添加测试日志
                Log::channel('video')->info('表单保存成功，准备调用视频生成API', [
                    'form_key' => $form->getKey(),
                    'is_editing' => $form->isEditing(),
                    'time' => date('Y-m-d H:i:s')
                ]);

                // 如果是新建视频，调用视频生成API
                if (!$form->isEditing()) {
                    try {
                        // 直接写入日志文件，确保日志目录存在
                        $logDir = storage_path('logs/video');
                        if (!is_dir($logDir)) {
                            mkdir($logDir, 0777, true);
                        }

                        // 写入日志文件
                        $logFile = $logDir . '/form_saved_debug.log';
                        $logContent = date('Y-m-d H:i:s') . " - 准备调用视频生成API\n";
                        $logContent .= "视频ID: " . $form->getKey() . "\n";
                        $logContent .= "----------------------------------------\n";
                        file_put_contents($logFile, $logContent, FILE_APPEND);

                        // 获取视频记录
                        $video = \App\Models\Video::findOrFail($form->getKey());

                        // 使用视频生成服务创建任务
                        $service = new \App\AI\Services\VideoGenerationService();
                        $result = $service->createVideoTask(
                            $video->cover_image_url,
                            $video->prompt,
                            $video->customer_id
                        );

                        // 更新视频记录的task_id
                        if (isset($result['task_id'])) {
                            $video->task_id = $result['task_id'];
                            $video->create_result = json_encode($result);
                            $video->save();
                        }

                        // 记录调用结果
                        $logContent = date('Y-m-d H:i:s') . " - 视频生成API调用完成\n";
                        $logContent .= "视频ID: " . $form->getKey() . "\n";
                        $logContent .= "结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
                        $logContent .= "----------------------------------------\n";
                        file_put_contents($logFile, $logContent, FILE_APPEND);

                        // 显示成功消息
                        $successMessage = '
                            <div>
                                <p><strong>视频生成任务已创建</strong></p>
                                <p>任务ID: ' . ($result['task_id'] ?? '未知') . '</p>
                                <p>视频生成需要一些时间，您可以：</p>
                                <ul>
                                    <li>在列表页使用"查询状态"按钮查询单个视频状态</li>
                                    <li>使用"批量查询状态"按钮查询所有待处理视频</li>
                                    <li>等待定时任务自动查询（如已配置）</li>
                                </ul>
                            </div>
                        ';

                        admin_success('视频生成任务已创建', $successMessage);
                    } catch (\Exception $e) {
                        // 记录错误信息
                        $logContent = date('Y-m-d H:i:s') . " - 视频生成API调用失败\n";
                        $logContent .= "视频ID: " . $form->getKey() . "\n";
                        $logContent .= "错误: " . $e->getMessage() . "\n";
                        $logContent .= "堆栈: " . $e->getTraceAsString() . "\n";
                        $logContent .= "----------------------------------------\n";
                        file_put_contents($logFile, $logContent, FILE_APPEND);

                        // 使用 Laravel 日志系统记录错误
                        Log::channel('video')->error('视频生成API调用失败', [
                            'video_id' => $form->getKey(),
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);

                        // 显示错误消息
                        admin_error('视频生成任务创建失败', $e->getMessage());
                    }
                }

                return $form;
            });

            // 设置表单标题
            if ($isEditing) {
                $form->title('编辑视频');
            } else {

                Log::channel('video')->info('请求结果：创建AI视频');
                $form->title('创建AI视频');
            }
        });
    }

    /**
     * 调用视频生成API
     */
    protected function callVideoGenerationApi($videoId)
    {
        // 直接写入日志文件，确保日志目录存在
        $logDir = storage_path('logs/video');
        if (!is_dir($logDir)) {
            mkdir($logDir, 0777, true);
        }

        // 写入日志文件
        $logFile = $logDir . '/call_api_debug.log';
        $logContent = date('Y-m-d H:i:s') . " - 开始调用视频生成API\n";
        $logContent .= "视频ID: " . $videoId . "\n";
        $logContent .= "----------------------------------------\n";
        file_put_contents($logFile, $logContent, FILE_APPEND);

        // 使用 Laravel 日志系统记录日志
        Log::channel('video')->info('开始调用视频生成API', [
            'video_id' => $videoId,
            'time' => date('Y-m-d H:i:s')
        ]);

        // 添加测试日志
        Log::channel('video')->info('开始调用视频生成API', [
            'video_id' => $videoId,
            'time' => date('Y-m-d H:i:s')
        ]);

        try {
            // 记录查询视频记录前的日志
            $logContent = date('Y-m-d H:i:s') . " - 准备查询视频记录\n";
            $logContent .= "视频ID: " . $videoId . "\n";
            $logContent .= "----------------------------------------\n";
            file_put_contents($logFile, $logContent, FILE_APPEND);

            Log::channel('video')->info('准备查询视频记录', [
                'video_id' => $videoId
            ]);

            $video = \App\Models\Video::findOrFail($videoId);

            // 记录查询视频记录后的日志
            $logContent = date('Y-m-d H:i:s') . " - 查询视频记录成功\n";
            $logContent .= "视频ID: " . $videoId . "\n";
            $logContent .= "封面图片URL: " . $video->cover_image_url . "\n";
            $logContent .= "提示词: " . $video->prompt . "\n";
            $logContent .= "----------------------------------------\n";
            file_put_contents($logFile, $logContent, FILE_APPEND);

            Log::channel('video')->info('查询视频记录成功', [
                'video_id' => $videoId,
                'cover_image_url' => $video->cover_image_url,
                'prompt' => $video->prompt
            ]);

            // 检查是否有可用的AI模型
            $logContent = date('Y-m-d H:i:s') . " - 准备查询可用的AI模型\n";
            $logContent .= "----------------------------------------\n";
            file_put_contents($logFile, $logContent, FILE_APPEND);

            Log::channel('video')->info('准备查询可用的AI模型');

            $aiModel = \App\AI\Models\AiModel::enabled()->first();

            if (!$aiModel) {
                $logContent = date('Y-m-d H:i:s') . " - 没有可用的AI模型配置\n";
                $logContent .= "----------------------------------------\n";
                file_put_contents($logFile, $logContent, FILE_APPEND);

                Log::channel('video')->error('没有可用的AI模型配置');
                throw new \Exception('没有可用的AI模型配置，请先在AI模型管理中配置并启用至少一个模型');
            }

            $logContent = date('Y-m-d H:i:s') . " - 查询AI模型成功\n";
            $logContent .= "模型ID: " . $aiModel->id . "\n";
            $logContent .= "模型名称: " . $aiModel->model_name . "\n";
            $logContent .= "域名: " . $aiModel->domain . "\n";
            $logContent .= "----------------------------------------\n";
            file_put_contents($logFile, $logContent, FILE_APPEND);

            Log::channel('video')->info('查询AI模型成功', [
                'model_id' => $aiModel->id,
                'model_name' => $aiModel->model_name,
                'domain' => $aiModel->domain
            ]);

            // 使用视频生成服务创建任务
            $logContent = date('Y-m-d H:i:s') . " - 准备创建视频生成任务\n";
            $logContent .= "封面图片URL: " . $video->cover_image_url . "\n";
            $logContent .= "提示词: " . $video->prompt . "\n";
            $logContent .= "客户ID: " . ($video->customer_id ?? '未设置') . "\n";
            $logContent .= "----------------------------------------\n";
            file_put_contents($logFile, $logContent, FILE_APPEND);

            Log::channel('video')->info('准备创建视频生成任务', [
                'cover_image_url' => $video->cover_image_url,
                'prompt' => $video->prompt,
                'customer_id' => $video->customer_id
            ]);

            $service = new VideoGenerationService();

            $logContent = date('Y-m-d H:i:s') . " - VideoGenerationService实例已创建\n";
            $logContent .= "----------------------------------------\n";
            file_put_contents($logFile, $logContent, FILE_APPEND);

            Log::channel('video')->info('VideoGenerationService实例已创建');

            $result = $service->createVideoTask(
                $video->cover_image_url,
                $video->prompt,
                $video->customer_id
            );

            $logContent = date('Y-m-d H:i:s') . " - 视频生成任务已创建\n";
            $logContent .= "结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
            $logContent .= "----------------------------------------\n";
            file_put_contents($logFile, $logContent, FILE_APPEND);

            Log::channel('video')->info('视频生成任务已创建', [
                'result' => $result
            ]);

            // 确保任务ID已保存到数据库
            if (isset($result['task_id']) && $result['task_id']) {
                // 再次检查视频记录的task_id是否已更新
                $logContent = date('Y-m-d H:i:s') . " - 准备检查视频记录的task_id是否已更新\n";
                $logContent .= "视频ID: " . $videoId . "\n";
                $logContent .= "任务ID: " . $result['task_id'] . "\n";
                $logContent .= "----------------------------------------\n";
                file_put_contents($logFile, $logContent, FILE_APPEND);

                $updatedVideo = \App\Models\Video::findOrFail($videoId);
                if (empty($updatedVideo->task_id)) {
                    // 如果task_id为空，手动更新
                    $logContent = date('Y-m-d H:i:s') . " - 需要手动更新视频任务ID\n";
                    $logContent .= "视频ID: " . $videoId . "\n";
                    $logContent .= "原任务ID: " . ($updatedVideo->task_id ?? '未设置') . "\n";
                    $logContent .= "新任务ID: " . $result['task_id'] . "\n";
                    $logContent .= "----------------------------------------\n";
                    file_put_contents($logFile, $logContent, FILE_APPEND);

                    $updatedVideo->task_id = $result['task_id'];
                    $updatedVideo->create_result = json_encode($result);
                    $updatedVideo->save();

                    $logContent = date('Y-m-d H:i:s') . " - 手动更新视频任务ID成功\n";
                    $logContent .= "视频ID: " . $videoId . "\n";
                    $logContent .= "任务ID: " . $result['task_id'] . "\n";
                    $logContent .= "----------------------------------------\n";
                    file_put_contents($logFile, $logContent, FILE_APPEND);

                    Log::channel('video')->info('手动更新视频任务ID', [
                        'video_id' => $videoId,
                        'task_id' => $result['task_id']
                    ]);
                } else {
                    $logContent = date('Y-m-d H:i:s') . " - 无需手动更新视频任务ID\n";
                    $logContent .= "视频ID: " . $videoId . "\n";
                    $logContent .= "任务ID: " . ($updatedVideo->task_id ?? '未设置') . "\n";
                    $logContent .= "----------------------------------------\n";
                    file_put_contents($logFile, $logContent, FILE_APPEND);
                }
            } else {
                $logContent = date('Y-m-d H:i:s') . " - 结果中没有任务ID\n";
                $logContent .= "视频ID: " . $videoId . "\n";
                $logContent .= "结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
                $logContent .= "----------------------------------------\n";
                file_put_contents($logFile, $logContent, FILE_APPEND);
            }

            // 显示成功消息
            $successMessage = '
                <div>
                    <p><strong>视频生成任务已创建</strong></p>
                    <p>任务ID: ' . ($result['task_id'] ?? '未知') . '</p>
                    <p>使用模型: ' . $aiModel->model_name . '</p>
                    <p>视频生成需要一些时间，您可以：</p>
                    <ul>
                        <li>在列表页使用"查询状态"按钮查询单个视频状态</li>
                        <li>使用"批量查询状态"按钮查询所有待处理视频</li>
                        <li>等待定时任务自动查询（如已配置）</li>
                    </ul>
                </div>
            ';

            admin_success('视频生成任务已创建', $successMessage);

            // 再次获取最新的视频记录
            $latestVideo = \App\Models\Video::findOrFail($videoId);

            Log::channel('video')->info("视频生成任务已创建", [
                'video_id' => $videoId,
                'task_id' => $result['task_id'] ?? '未知',
                'saved_task_id' => $latestVideo->task_id,
                'model' => $aiModel->model_name,
                'prompt' => $video->prompt
            ]);

            // 确保返回结果中包含视频ID
            if (!isset($result['video_id'])) {
                $result['video_id'] = $videoId;
            }

            // 如果返回结果中的视频ID与当前视频ID不同，记录警告
            if (isset($result['video_id']) && $result['video_id'] != $videoId) {
                Log::channel('video')->warning("返回结果中的视频ID与当前视频ID不同", [
                    'current_video_id' => $videoId,
                    'result_video_id' => $result['video_id']
                ]);
            }

            return $result;
        } catch (\Exception $e) {
            $logContent = date('Y-m-d H:i:s') . " - 视频生成任务创建失败\n";
            $logContent .= "视频ID: " . $videoId . "\n";
            $logContent .= "错误: " . $e->getMessage() . "\n";
            $logContent .= "堆栈: " . $e->getTraceAsString() . "\n";
            $logContent .= "----------------------------------------\n";
            file_put_contents($logFile, $logContent, FILE_APPEND);

            admin_error('视频生成任务创建失败', $e->getMessage());
            Log::channel('video')->error("视频生成任务创建失败", ['video_id' => $videoId, 'error' => $e->getMessage()]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 查询视频状态（重定向版本）
     */
    public function queryStatus($id)
    {
        try {
            $service = new VideoGenerationService();
            $result = $service->queryVideoStatus($id);

            admin_success('视频状态已更新', '状态: ' . ($result['status'] ?? '未知'));

            return redirect()->back();
        } catch (\Exception $e) {
            admin_error('查询视频状态失败', $e->getMessage());

            return redirect()->back();
        }
    }

    /**
     * 查询视频状态（AJAX版本）
     */
    public function queryStatusAjax($id)
    {
        try {
            $video = \App\Models\Video::findOrFail($id);
            $oldStatus = $video->status;

            $service = new VideoGenerationService();
            $result = $service->queryVideoStatus($id);

            // 获取更新后的视频信息
            $video = \App\Models\Video::findOrFail($id);

            // 准备响应消息
            $message = '<div><strong>视频状态已更新</strong></div>';
            $message .= '<div>ID: ' . $video->id . '</div>';
            $message .= '<div>原状态: ' . $this->getStatusText($oldStatus) . '</div>';
            $message .= '<div>当前状态: ' . $this->getStatusText($video->status) . '</div>';

            if ($video->status === 'success' && !empty($video->video_url)) {
                $message .= '<div>视频URL已更新</div>';
            }

            return response()->json([
                'status' => true,
                'message' => $message,
                'data' => [
                    'video_id' => $video->id,
                    'old_status' => $oldStatus,
                    'new_status' => $video->status,
                    'video_url' => $video->video_url
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => '查询视频状态失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 处理视频生成回调
     */
    public function handleCallback(Request $request)
    {
        try {
            $callbackData = $request->all();

            Log::channel('video')->info('收到视频生成回调', ['data' => $callbackData]);

            $service = new VideoGenerationService();
            $result = $service->handleCallback($callbackData);

            return response()->json([
                'success' => true,
                'message' => '回调处理成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::channel('video')->error('处理视频生成回调失败', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量查询视频状态（页面版本）
     */
    public function batchQueryStatus(Content $content)
    {
        try {
            // 执行命令
            $exitCode = Artisan::call('video:query-status');
            $output = Artisan::output();

            // 获取视频状态统计
            $service = new VideoStatusService();
            $stats = $service->getStatusStats();

            // 创建卡片显示结果
            $card = new Card();
            $card->title('批量查询视频状态');

            // 添加统计信息
            $statsHtml = "<div class='mb-3'>";
            $statsHtml .= "<div class='mb-2'><strong>视频总数:</strong> {$stats['total']}</div>";
            $statsHtml .= "<div class='mb-2'><strong>待处理视频:</strong> {$stats['pending']}</div>";
            $statsHtml .= "<div class='mb-2'><strong>成功视频:</strong> {$stats['success']}</div>";
            $statsHtml .= "<div class='mb-2'><strong>失败视频:</strong> {$stats['failed']}</div>";
            $statsHtml .= "<div class='mb-2'><strong>启用视频:</strong> {$stats['active']}</div>";
            $statsHtml .= "<div class='mb-2'><strong>禁用视频:</strong> {$stats['inactive']}</div>";
            $statsHtml .= "</div>";

            // 添加命令输出
            $outputHtml = "<div class='mt-3'>";
            $outputHtml .= "<h5>命令输出:</h5>";
            $outputHtml .= "<pre style='background-color: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 300px; overflow: auto;'>";
            $outputHtml .= htmlspecialchars($output);
            $outputHtml .= "</pre>";
            $outputHtml .= "</div>";

            // 添加日志文件路径信息
            $logFilePaths = [
                'Laravel 日志文件' => storage_path('logs/laravel-' . date('Y-m-d') . '.log'),
                '定时任务日志文件' => storage_path('logs/schedule-' . date('Y-m-d') . '.log'),
                '视频状态日志文件' => storage_path('logs/video-status-' . date('Y-m-d') . '.log')
            ];

            $logHtml = "<div class='mt-4'>";
            $logHtml .= "<h5>日志文件路径:</h5>";
            $logHtml .= "<ul class='list-group'>";
            foreach ($logFilePaths as $name => $path) {
                $exists = file_exists($path) ? '存在' : '不存在';
                $size = file_exists($path) ? round(filesize($path) / 1024, 2) . ' KB' : '0 KB';
                $logHtml .= "<li class='list-group-item d-flex justify-content-between align-items-center'>";
                $logHtml .= "<span><strong>{$name}:</strong> {$path}</span>";
                $logHtml .= "<span class='badge " . (file_exists($path) ? 'bg-success' : 'bg-danger') . "'>{$exists} ({$size})</span>";
                $logHtml .= "</li>";
            }
            $logHtml .= "</ul>";
            $logHtml .= "</div>";

            // 添加返回按钮
            $buttonHtml = "<div class='mt-3'>";
            $buttonHtml .= "<a href='" . admin_url('ai/video') . "' class='btn btn-primary'>返回列表</a>";
            $buttonHtml .= "</div>";

            $card->content($statsHtml . $outputHtml . $logHtml . $buttonHtml);

            return $content
                ->title('批量查询视频状态')
                ->description('执行批量查询视频状态任务')
                ->body($card);
        } catch (\Exception $e) {
            admin_error('批量查询视频状态失败', $e->getMessage());

            return redirect()->route('admin.ai.video.index');
        }
    }

    /**
     * 批量查询视频状态（AJAX版本）
     */
    public function batchQueryStatusAjax()
    {
        // 记录请求开始
        Log::channel('video')->info('批量查询视频状态AJAX请求开始', [
            'time' => date('Y-m-d H:i:s'),
            'url' => request()->fullUrl(),
            'method' => request()->method(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        try {
            // 获取视频状态服务
            $service = new VideoStatusService();
            Log::channel('video')->info('VideoStatusService实例已创建');

            // 获取待处理视频的数量
            $pendingCount = $service->getPendingCount();
            Log::channel('video')->info('获取到待处理视频数量', ['count' => $pendingCount]);

            // 如果没有待处理的视频，直接返回
            if ($pendingCount === 0) {
                Log::channel('video')->info('没有待处理的视频，返回空结果');

                return response()->json([
                    'status' => true,
                    'message' => '没有待处理的视频',
                    'data' => [
                        'total' => 0,
                        'success' => 0,
                        'failed' => 0,
                        'pending' => 0,
                        'error' => 0
                    ]
                ]);
            }

            // 批量查询视频状态（使用改进后的方法，一条一条地查询）
            Log::channel('video')->info('开始批量查询视频状态（逐条查询）', ['limit' => 100]);
            $stats = $service->batchQueryStatus(100); // 每次最多查询100个
            Log::channel('video')->info('批量查询视频状态完成', ['stats' => $stats]);

            // 获取视频状态统计
            Log::channel('video')->info('开始获取视频状态统计');
            $allStats = $service->getStatusStats();
            Log::channel('video')->info('获取视频状态统计完成', ['all_stats' => $allStats]);

            // 记录日志
            Log::channel('video')->info('批量查询视频状态AJAX请求完成', [
                'stats' => $stats,
                'all_stats' => $allStats,
                'time' => date('Y-m-d H:i:s')
            ]);

            // 返回成功响应
            $response = [
                'status' => true,
                'message' => '批量查询视频状态完成',
                'data' => $stats,
                'all_stats' => $allStats
            ];

            Log::channel('video')->info('返回成功响应', ['response' => $response]);

            return response()->json($response);
        } catch (\Exception $e) {
            Log::channel('video')->error('批量查询视频状态AJAX请求失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'time' => date('Y-m-d H:i:s')
            ]);

            // 尝试获取更详细的错误信息
            $errorDetails = '';
            if ($e instanceof \GuzzleHttp\Exception\RequestException && $e->hasResponse()) {
                $errorDetails = $e->getResponse()->getBody()->getContents();
                Log::channel('video')->error('API请求错误详情', ['details' => $errorDetails]);
            }

            // 返回错误响应
            $response = [
                'status' => false,
                'message' => '批量查询视频状态失败: ' . $e->getMessage(),
                'error_details' => $errorDetails
            ];

            Log::channel('video')->error('返回错误响应', ['response' => $response]);

            return response()->json($response, 500);
        }
    }

    /**
     * 管理定时任务
     */
    public function manageScheduleTask(Content $content)
    {
        try {
            // 查找视频状态查询定时任务
            $task = ArtisanScheduleTask::where('command', 'video:query-status')->first();

            // 创建表单
            $form = new WidgetForm();
            $form->title('管理视频状态查询定时任务');

            if ($task) {
                // 显示任务信息
                $form->display('id', 'ID')->value($task->id);
                $form->display('command', '命令')->value($task->command);
                $form->text('description', '描述')->value($task->description);
                $form->text('expression', '时间表达式')->value($task->expression)
                    ->help('Cron表达式，例如：*/5 * * * * 表示每5分钟执行一次');
                $form->switch('status', '是否启用')->value($task->status);
                $form->switch('dont_overlap', '避免重复执行')->value($task->dont_overlap);
                $form->switch('run_in_maintenance', '维护模式也执行')->value($task->run_in_maintenance);
                $form->display('created_at', '创建时间')->value($task->created_at);
                $form->display('updated_at', '更新时间')->value($task->updated_at);

                // 设置表单提交地址
                $form->action(admin_url('ai/video/update-schedule-task/' . $task->id));
            } else {
                // 创建新任务
                $form->display('command', '命令')->value('video:query-status');
                $form->text('description', '描述')->default('查询待处理视频的状态');
                $form->text('expression', '时间表达式')->default('*/5 * * * *')
                    ->help('Cron表达式，例如：*/5 * * * * 表示每5分钟执行一次');
                $form->switch('status', '是否启用')->default(1);
                $form->switch('dont_overlap', '避免重复执行')->default(1);
                $form->switch('run_in_maintenance', '维护模式也执行')->default(1);

                // 设置表单提交地址
                $form->action(admin_url('ai/video/create-schedule-task'));
            }

            // 添加CSRF令牌
            $form->hidden('_token')->default(csrf_token());
            $form->method('POST');

            // 添加说明
            $description = new Card();
            $description->title('定时任务说明');
            $description->content(<<<HTML
<p>此定时任务用于自动查询待处理视频的状态，更新视频的生成结果。</p>
<p>时间表达式说明：</p>
<ul>
    <li><code>* * * * *</code> - 每分钟执行一次</li>
    <li><code>*/5 * * * *</code> - 每5分钟执行一次</li>
    <li><code>0 * * * *</code> - 每小时的第0分钟执行</li>
    <li><code>0 0 * * *</code> - 每天的0点0分执行</li>
</ul>
<p>更多Cron表达式说明，请参考：<a href="https://crontab.guru/" target="_blank">https://crontab.guru/</a></p>
HTML);

            return $content
                ->title('管理视频状态查询定时任务')
                ->description('配置视频状态查询定时任务')
                ->body([$description, $form]);
        } catch (\Exception $e) {
            admin_error('管理定时任务失败', $e->getMessage());

            return redirect()->route('admin.ai.video.index');
        }
    }

    /**
     * 创建定时任务
     */
    public function createScheduleTask(Request $request)
    {
        try {
            // 创建定时任务
            $task = new ArtisanScheduleTask();
            $task->command = 'video:query-status';
            $task->description = $request->input('description', '查询待处理视频的状态');
            $task->parameters = '[]';
            $task->expression = $request->input('expression', '*/5 * * * *');
            $task->status = $request->input('status', 0);
            $task->dont_overlap = $request->input('dont_overlap', 0);
            $task->run_in_maintenance = $request->input('run_in_maintenance', 0);
            $task->save();

            admin_success('创建定时任务成功', '视频状态查询定时任务已创建');

            return redirect()->route('admin.ai.video.index');
        } catch (\Exception $e) {
            admin_error('创建定时任务失败', $e->getMessage());

            return redirect()->back()->withInput();
        }
    }

    /**
     * 更新定时任务
     */
    public function updateScheduleTask(Request $request, $id)
    {
        try {
            // 更新定时任务
            $task = ArtisanScheduleTask::findOrFail($id);
            $task->description = $request->input('description', $task->description);
            $task->expression = $request->input('expression', $task->expression);
            $task->status = $request->input('status', 0);
            $task->dont_overlap = $request->input('dont_overlap', 0);
            $task->run_in_maintenance = $request->input('run_in_maintenance', 0);
            $task->save();

            admin_success('更新定时任务成功', '视频状态查询定时任务已更新');

            return redirect()->route('admin.ai.video.index');
        } catch (\Exception $e) {
            admin_error('更新定时任务失败', $e->getMessage());

            return redirect()->back()->withInput();
        }
    }

    /**
     * 测试日志
     */
    public function testLog()
    {
        // 使用不同的日志通道记录日志
        Log::info('测试 Log::info');
        Log::channel('single')->info('测试 Log::channel(single)->info');
        Log::channel('daily')->info('测试 Log::channel(daily)->info');
        Log::channel('video')->info('测试 Log::channel(video)->info');

        // 返回成功消息
        return response()->json([
            'status' => true,
            'message' => '测试日志已记录',
            'time' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 测试视频生成
     */
    public function testVideo()
    {
        try {
            // 直接写入日志文件，确保日志目录存在
            $logDir = storage_path('logs/video');
            if (!is_dir($logDir)) {
                mkdir($logDir, 0777, true);
            }

            // 写入日志文件
            $logFile = $logDir . '/test_video.log';
            $logContent = date('Y-m-d H:i:s') . " - 开始测试视频生成\n";
            $logContent .= "----------------------------------------\n";
            file_put_contents($logFile, $logContent, FILE_APPEND);

            // 使用 Laravel 日志系统
            Log::channel('video')->info('开始测试视频生成');

            // 查询一个视频记录
            $video = \App\Models\Video::first();

            if (!$video) {
                $logContent = date('Y-m-d H:i:s') . " - 没有找到视频记录\n";
                $logContent .= "----------------------------------------\n";
                file_put_contents($logFile, $logContent, FILE_APPEND);

                Log::channel('video')->error('没有找到视频记录');

                return response()->json([
                    'status' => false,
                    'message' => '没有找到视频记录，请先创建一个视频记录'
                ]);
            }

            $logContent = date('Y-m-d H:i:s') . " - 找到视频记录\n";
            $logContent .= "视频ID: " . $video->id . "\n";
            $logContent .= "封面图片URL: " . $video->cover_image_url . "\n";
            $logContent .= "提示词: " . $video->prompt . "\n";
            $logContent .= "----------------------------------------\n";
            file_put_contents($logFile, $logContent, FILE_APPEND);

            Log::channel('video')->info('找到视频记录', [
                'video_id' => $video->id,
                'cover_image_url' => $video->cover_image_url,
                'prompt' => $video->prompt
            ]);

            // 查询可用的AI模型
            $aiModel = \App\AI\Models\AiModel::enabled()->first();

            if (!$aiModel) {
                $logContent = date('Y-m-d H:i:s') . " - 没有找到可用的AI模型\n";
                $logContent .= "----------------------------------------\n";
                file_put_contents($logFile, $logContent, FILE_APPEND);

                Log::channel('video')->error('没有找到可用的AI模型');

                return response()->json([
                    'status' => false,
                    'message' => '没有找到可用的AI模型，请先在AI模型管理中配置并启用至少一个模型'
                ]);
            }

            $logContent = date('Y-m-d H:i:s') . " - 找到可用的AI模型\n";
            $logContent .= "模型ID: " . $aiModel->id . "\n";
            $logContent .= "模型名称: " . $aiModel->model_name . "\n";
            $logContent .= "域名: " . $aiModel->domain . "\n";
            $logContent .= "----------------------------------------\n";
            file_put_contents($logFile, $logContent, FILE_APPEND);

            Log::channel('video')->info('找到可用的AI模型', [
                'model_id' => $aiModel->id,
                'model_name' => $aiModel->model_name,
                'domain' => $aiModel->domain
            ]);

            // 创建视频生成服务实例
            $service = new \App\AI\Services\VideoGenerationService();

            $logContent = date('Y-m-d H:i:s') . " - VideoGenerationService实例已创建\n";
            $logContent .= "----------------------------------------\n";
            file_put_contents($logFile, $logContent, FILE_APPEND);

            Log::channel('video')->info('VideoGenerationService实例已创建');

            // 调用创建视频任务方法
            $result = $service->createVideoTask(
                $video->cover_image_url,
                $video->prompt,
                $video->customer_id
            );

            $logContent = date('Y-m-d H:i:s') . " - 视频生成任务已创建\n";
            $logContent .= "结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
            $logContent .= "----------------------------------------\n";
            file_put_contents($logFile, $logContent, FILE_APPEND);

            Log::channel('video')->info('视频生成任务已创建', [
                'result' => $result
            ]);

            // 返回成功消息
            return response()->json([
                'status' => true,
                'message' => '视频生成任务已创建',
                'result' => $result
            ]);
        } catch (\Exception $e) {
            $logContent = date('Y-m-d H:i:s') . " - 视频生成任务创建失败\n";
            $logContent .= "错误: " . $e->getMessage() . "\n";
            $logContent .= "堆栈: " . $e->getTraceAsString() . "\n";
            $logContent .= "----------------------------------------\n";
            file_put_contents($logFile, $logContent, FILE_APPEND);

            Log::channel('video')->error('视频生成任务创建失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 返回错误消息
            return response()->json([
                'status' => false,
                'message' => '视频生成任务创建失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 创建测试视频（AJAX请求）
     */
    public function createTestVideosAjax()
    {
        try {
            // 记录请求信息
            Log::channel('video')->info('创建测试视频AJAX请求开始', [
                'time' => now()->format('Y-m-d H:i:s'),
                'url' => request()->url(),
                'method' => request()->method(),
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent()
            ]);

            // 获取参数
            $count = request()->input('count', 5);

            // 创建视频状态服务实例
            $service = new VideoStatusService();
            Log::channel('video')->info('VideoStatusService实例已创建');

            // 创建测试视频
            $result = $service->createTestVideos($count);
            Log::channel('video')->info('创建测试视频完成', ['result' => $result]);

            return response()->json($result);
        } catch (\Exception $e) {
            Log::channel('video')->error('创建测试视频出错', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => false,
                'message' => '创建测试视频失败: ' . $e->getMessage()
            ], 500);
        }
    }
}
