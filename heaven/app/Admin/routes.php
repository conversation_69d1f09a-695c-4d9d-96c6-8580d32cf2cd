<?php

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Dcat\Admin\Admin;

Admin::routes();

Route::group([
    'prefix'     => config('admin.route.prefix'),
    'namespace'  => config('admin.route.namespace'),
    'middleware' => config('admin.route.middleware'),
], function (Router $router) {

    $router->get('/', 'HomeController@index');

    // 自定义扩展
    // 自动留言任务创建
    $router->get('crontab/space-task/create', 'CrontabController@createSpaceTask');
    // 定时任务
    $router->resource('crontab', 'CrontabController')->except('create');

    //公共管理
    $router->resource('bgm', 'BGMController');
    $router->resource('banner', 'BannerController');
    $router->resource('comment', 'CommentController');
    $router->resource('photo', 'PhotoController');
    $router->resource('comments_common', 'CommentsCommonController');
    $router->resource('comments-reference', 'CommentsReferenceController');
    $router->resource('tags', 'TagController')->except('show');

    //用户管理
    $router->resource('user', 'User\UserController');
    $router->resource('user_withdraw', 'User\UserWithdrawController');
    $router->resource('inviter', 'InviterController');
    $router->resource('user-money', 'Money\UserMoneyController');
    $router->resource('sms-code', 'SmsCodeController');
    $router->resource('user-bill', 'User\UserBillController')->only('index');

    // 生成虚拟用户
    $router->get('fake/user-generator', 'FakeDataController@userGenerator')
        ->name('fake.user_generator');
    // 爬取今日头条PC站评论
    $router->get('fake/pull-toutiao-comment', 'FakeDataController@pullCommentFromToutiao')
        ->name('fake.pull_toutiao_comment');

    //签到管理
    $router->resource('user-signin', 'Signin\UserSigninController');
    $router->resource('user-signin-log', 'Signin\UserSigninLogController');

    //文章管理
    $router->resource('article', 'Article\ArticleController');
    $router->resource('article_text', 'Article\ArticleController');
    $router->resource('article_tip', 'Article\ArticleController');

    //文章分类
    $router->resource('article-category', 'Article\ArticleCategoryController');

    //首页提醒
    $router->resource('tip', 'TipController');

    //纪念堂管理
    $router->resource('space', 'Space\SpaceController');
    $router->resource('space-level', 'Space\SpaceLevelController');
    $router->resource('space-share', 'Space\SpaceShareController');

    // 装饰管理
    $router->resource('space-decoration', 'Space\SpaceDecorationController');
    $router->resource('position', 'Space\PositionController');
    $router->resource('space-background', 'Space\SpaceBackgroundController');

    //纪念人物
    $router->resource('dwellers', 'DwellerController');

    //商品管理
    $router->resource('goods', 'Goods\GoodsController');
    $router->resource('goods-category', 'Goods\GoodsCategoryController');
    $router->resource('goods-batch', 'Goods\GoodsBatchController');
    $router->resource('goods-log', 'Goods\GoodsLogController')->only('index');

    //订单管理
    $router->resource('order', 'OrderController');

    //系统设置
    $router->resource('admin_setting', 'AdminSettingController');

    // 活跃度统计记录
    $router->get('space-activity', 'ActivityCountController@spaceList');
    $router->get('user-activity', 'ActivityCountController@userList');

    // 云讣告 模板
    $router->resource('obituary_template', 'Obituary\ObituaryTemplateController');

    // 云讣告
    $router->resource('obituary', 'Obituary\ObituaryController');

    // Api
    $router->get('api/city', 'ApiController@city')->name('api.city');
    $router->get('api/area', 'ApiController@area')->name('api.area');

    // 轮播图管理
    $router->resource('carousel', 'CarouselController');

    // AI相关功能
    $router->group(['namespace' => 'AI', 'prefix' => 'ai'], function ($router) {
        // AI模型管理
        $router->resource('model', 'AiModelController');

        // 视频管理
        // 先定义固定路径的路由，避免被 {id} 参数路由捕获
        $router->post('video/callback', 'VideoController@handleCallback');
        $router->get('video/batch-query-status', 'VideoController@batchQueryStatus');
        $router->get('video/batch-query-status-ajax', 'VideoController@batchQueryStatusAjax');
        $router->get('video/manage-schedule-task', 'VideoController@manageScheduleTask');
        $router->post('video/create-schedule-task', 'VideoController@createScheduleTask');

        // 然后定义带参数的路由
        $router->get('video/{id}/review', 'VideoController@review');
        $router->post('video/{id}/review', 'VideoController@reviewStore');
        $router->get('video/{id}/query-status', 'VideoController@queryStatus');
        $router->get('video/{id}/query-status-ajax', 'VideoController@queryStatusAjax');
        $router->post('video/update-schedule-task/{id}', 'VideoController@updateScheduleTask');

        // 最后定义资源路由
        $router->resource('video', 'VideoController');

        // API测试
        $router->get('api-test', 'ApiTestController@index');
        $router->post('api-test/test', 'ApiTestController@test');

        // 简单API测试
        $router->get('simple-api-test', 'SimpleApiTestController@index');
        $router->post('simple-api-test/test', 'SimpleApiTestController@test');

        // 直接API测试
        $router->get('direct-api-test', 'DirectApiTestController@index');
        $router->post('direct-api-test/test', 'DirectApiTestController@test');

        // 测试日志
        $router->get('test-log', 'VideoController@testLog');

        // 测试视频生成
        $router->get('test-video', 'VideoController@testVideo');

        // 测试路由
        $router->get('test-route', function () {
            return response()->json([
                'status' => true,
                'message' => '测试路由成功',
                'time' => date('Y-m-d H:i:s')
            ]);
        });

        // 测试批量查询状态
        $router->get('test-batch-query', function () {
            try {
                // 记录请求开始
                \Illuminate\Support\Facades\Log::channel('video')->info('测试批量查询状态开始', [
                    'time' => date('Y-m-d H:i:s'),
                    'url' => request()->fullUrl(),
                    'method' => request()->method()
                ]);

                // 创建服务实例
                $service = new \App\AI\Services\VideoStatusService();

                // 获取待处理视频的数量
                $pendingCount = $service->getPendingCount();

                // 批量查询视频状态
                $stats = $service->batchQueryStatus(20);

                // 获取视频状态统计
                $allStats = $service->getStatusStats();

                // 记录日志
                \Illuminate\Support\Facades\Log::channel('video')->info('测试批量查询状态完成', [
                    'stats' => $stats,
                    'all_stats' => $allStats
                ]);

                return response()->json([
                    'status' => true,
                    'message' => '测试批量查询状态成功',
                    'data' => [
                        'pending_count' => $pendingCount,
                        'stats' => $stats,
                        'all_stats' => $allStats
                    ]
                ]);
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::channel('video')->error('测试批量查询状态失败', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                return response()->json([
                    'status' => false,
                    'message' => '测试批量查询状态失败: ' . $e->getMessage()
                ], 500);
            }
        });

        // 新的批量查询状态路由（不使用 video 前缀，避免与 video/{id} 冲突）
        $router->get('batch-query-video-status', 'VideoController@batchQueryStatusAjax');


    });

    // 产品管理
    $router->group(['namespace' => 'Product', 'prefix' => 'product'], function ($router) {
        $router->resource('categories', 'ProductCategoryController');
        $router->resource('products', 'ProductController');
    });
});
