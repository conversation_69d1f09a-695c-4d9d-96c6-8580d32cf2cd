<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Video;
use App\AI\Services\VideoStatusService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class QueryVideoStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'video:query-status {task_id? : 任务ID} {--tenant_id= : 租户ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '查询待处理视频的状态';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $startTime = microtime(true);
        $message = '开始查询待处理视频的状态...';
        $this->info($message);
        Log::info($message);

        try {
            // 测试数据库连接
            $this->testDatabaseConnection();

            $service = new VideoStatusService();

            // 尝试获取待处理视频数量，最多重试3次
            $pendingCount = 0;
            $maxRetries = 3;
            $retryCount = 0;

            while ($retryCount < $maxRetries) {
                try {
                    // 获取待处理视频的数量
                    $pendingCount = $service->getPendingCount();
                    $message = "找到 {$pendingCount} 个待处理的视频";
                    $this->info($message);
                    Log::info($message);
                    break; // 如果成功，跳出循环
                } catch (\Exception $e) {
                    $retryCount++;
                    $this->warn("获取待处理视频数量失败，正在重试 ({$retryCount}/{$maxRetries}): " . $e->getMessage());
                    Log::warning("获取待处理视频数量失败，正在重试 ({$retryCount}/{$maxRetries})", [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);

                    if ($retryCount >= $maxRetries) {
                        throw $e; // 重试次数用完，抛出异常
                    }

                    // 等待一段时间再重试
                    sleep(2);
                }
            }

            if ($pendingCount === 0) {
                $message = '没有待处理的视频，任务结束';
                $this->info($message);
                Log::info($message);
                return 0;
            }

            // 尝试批量查询视频状态，最多重试3次
            $stats = [];
            $retryCount = 0;

            while ($retryCount < $maxRetries) {
                try {
                    // 批量查询视频状态
                    $stats = $service->batchQueryStatus(100); // 每次最多查询100个
                    break; // 如果成功，跳出循环
                } catch (\Exception $e) {
                    $retryCount++;
                    $this->warn("批量查询视频状态失败，正在重试 ({$retryCount}/{$maxRetries}): " . $e->getMessage());
                    Log::warning("批量查询视频状态失败，正在重试 ({$retryCount}/{$maxRetries})", [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);

                    if ($retryCount >= $maxRetries) {
                        throw $e; // 重试次数用完，抛出异常
                    }

                    // 等待一段时间再重试
                    sleep(2);
                }
            }

            $message = "查询完成，总数: {$stats['total']}, 成功: {$stats['success']}, 失败: {$stats['failed']}, 处理中: {$stats['pending']}, 错误: {$stats['error']}";
            $this->info($message);
            Log::info($message);

            // 尝试获取视频状态统计，最多重试3次
            $allStats = [];
            $retryCount = 0;

            while ($retryCount < $maxRetries) {
                try {
                    // 获取视频状态统计
                    $allStats = $service->getStatusStats();
                    break; // 如果成功，跳出循环
                } catch (\Exception $e) {
                    $retryCount++;
                    $this->warn("获取视频状态统计失败，正在重试 ({$retryCount}/{$maxRetries}): " . $e->getMessage());
                    Log::warning("获取视频状态统计失败，正在重试 ({$retryCount}/{$maxRetries})", [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);

                    if ($retryCount >= $maxRetries) {
                        throw $e; // 重试次数用完，抛出异常
                    }

                    // 等待一段时间再重试
                    sleep(2);
                }
            }

            $message = "视频总数: {$allStats['total']}, 待处理: {$allStats['pending']}, 成功: {$allStats['success']}, 失败: {$allStats['failed']}";
            $this->info($message);
            Log::info($message);

            $message = "启用视频: {$allStats['active']}, 禁用视频: {$allStats['inactive']}";
            $this->info($message);
            Log::info($message);

            // 计算执行时间
            $executionTime = round(microtime(true) - $startTime, 2);
            $message = "任务执行完成，耗时: {$executionTime} 秒";
            $this->info($message);
            Log::info($message);

            return 0;
        } catch (\Exception $e) {
            $errorMessage = "执行查询视频状态任务时出错: {$e->getMessage()}";
            $this->error($errorMessage);
            Log::error($errorMessage, [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return 1;
        }
    }

    /**
     * 测试数据库连接
     *
     * @return void
     * @throws \Exception
     */
    private function testDatabaseConnection()
    {
        try {
            // 获取数据库连接信息
            $connection = config('database.default');
            $host = config("database.connections.{$connection}.host");
            $database = config("database.connections.{$connection}.database");

            $this->info("数据库连接信息: {$connection} / {$host} / {$database}");
            Log::info("数据库连接信息", [
                'connection' => $connection,
                'host' => $host,
                'database' => $database
            ]);

            // 测试数据库连接
            $result = DB::select('SELECT 1 as result');

            if ($result && isset($result[0]->result) && $result[0]->result === 1) {
                $this->info('数据库连接测试成功');
                Log::info('数据库连接测试成功');
            } else {
                throw new \Exception('数据库连接测试失败');
            }
        } catch (\Exception $e) {
            $this->error('数据库连接测试失败: ' . $e->getMessage());
            Log::error('数据库连接测试失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
}
