<?php

namespace App\Console\Commands;

use App\Models\Video;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ResetVideoStatusCommand extends Command
{
    protected $signature = 'video:reset-status {--failed : 只重置失败状态的视频}';
    protected $description = '重置视频状态为待处理';

    public function handle()
    {
        $this->info('开始重置视频状态...');

        try {
            // 构建查询
            $query = Video::query();
            if ($this->option('failed')) {
                $query->where('status', 'failed');
            }

            // 获取符合条件的视频
            $videos = $query->get();
            $count = $videos->count();

            if ($count === 0) {
                $this->info('没有找到需要重置的视频');
                return;
            }

            $this->info("找到 {$count} 个需要重置的视频");

            // 重置视频状态
            foreach ($videos as $video) {
                $video->status = 'pending';
                $video->task_result = null;
                $video->save();

                $this->info("重置视频状态成功: ID={$video->id}");
                Log::info('重置视频状态', [
                    'video_id' => $video->id,
                    'old_status' => 'failed',
                    'new_status' => 'pending'
                ]);
            }

            $this->info('视频状态重置完成');
        } catch (\Exception $e) {
            $this->error('重置视频状态失败: ' . $e->getMessage());
            Log::error('重置视频状态失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
} 