<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // 定时任务现在通过数据库配置管理，不在这里硬编码
        // 视频状态查询任务已移至 artisan_schedule_tasks 表中配置

        // 直接从数据库加载任务
        $this->loadTasksFromDatabase($schedule);
    }

    /**
     * 从数据库加载定时任务
     */
    private function loadTasksFromDatabase(Schedule $schedule)
    {
        try {
            $tasks = \App\Models\ArtisanScheduleTask::where('status', 1)->get();

            foreach ($tasks as $task) {
                $event = $schedule->command($task->command)
                    ->cron($task->expression)
                    ->name($task->description);

                if ($task->dont_overlap) {
                    $event->withoutOverlapping();
                }

                if ($task->run_in_maintenance) {
                    $event->evenInMaintenanceMode();
                }
            }
        } catch (\Exception $e) {
            // 如果数据库还没有准备好，忽略错误
            \Illuminate\Support\Facades\Log::error('Failed to load tasks from database: ' . $e->getMessage());
        }
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');
        $this->load(__DIR__.'/Central');
    }
}
