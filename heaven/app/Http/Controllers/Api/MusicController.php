<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\BGM;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MusicController extends Controller
{
    /**
     * 获取活跃的背景音乐列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function activeMusics(Request $request)
    {
        try {
            // 获取所有启用的背景音乐
            $musics = BGM::enable()->get();

            // 如果没有找到任何音乐，添加默认音乐
            if ($musics->isEmpty()) {
                // 返回默认音乐
                return response()->json([
                    [
                        'id' => 0,
                        'title' => '默认背景音乐',
                        'music_url' => 'https://bjetxgzv.cdn.bspapp.com/VKCEYUGU-uni-app-doc/a4b0600d-5e1f-4946-a280-c8975c2d5556.mp3',
                        'cover_image' => 'https://bjetxgzv.cdn.bspapp.com/VKCEYUGU-uni-app-doc/7fbf2e60-60eb-4cb6-b6b1-4335f6f24d1a.jpg',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]
                ]);
            }

            // 转换为前端需要的格式
            $result = $musics->map(function ($music) {
                // 处理音乐URL
                $musicUrl = $music->url;

                // 如果URL是相对路径（如 bgm/20231207/073172bd51a85dbffda8c991d05238e3.mp3），
                // 则直接使用，不需要转换为完整URL
                if ($musicUrl && !filter_var($musicUrl, FILTER_VALIDATE_URL) && !str_starts_with($musicUrl, '/')) {
                    // 保持原样，不做处理
                } else {
                    // 否则使用asset2url函数转换
                    $musicUrl = asset2url($music->url);

                    // 如果URL不包含文件扩展名，添加.mp3扩展名
                    if ($musicUrl && !preg_match('/\.(mp3|wav|ogg|aac|m4a)(\?|$)/i', $musicUrl)) {
                        $musicUrl = preg_replace('/(\?|$)/', '.mp3$1', $musicUrl);
                    }
                }

                // 如果URL仍然无效，使用默认音乐URL
                if (!$musicUrl) {
                    $musicUrl = 'https://bjetxgzv.cdn.bspapp.com/VKCEYUGU-uni-app-doc/a4b0600d-5e1f-4946-a280-c8975c2d5556.mp3';
                }

                return [
                    'id' => $music->id,
                    'title' => $music->name,
                    'music_url' => $musicUrl,
                    'cover_image' => asset2url($music->img) ?: 'https://bjetxgzv.cdn.bspapp.com/VKCEYUGU-uni-app-doc/7fbf2e60-60eb-4cb6-b6b1-4335f6f24d1a.jpg',
                    'created_at' => $music->created_at,
                    'updated_at' => $music->updated_at,
                ];
            });

            // 添加默认音乐作为第一个选项
            $result->prepend([
                'id' => 0,
                'title' => '默认背景音乐',
                'music_url' => 'https://bjetxgzv.cdn.bspapp.com/VKCEYUGU-uni-app-doc/a4b0600d-5e1f-4946-a280-c8975c2d5556.mp3',
                'cover_image' => 'https://bjetxgzv.cdn.bspapp.com/VKCEYUGU-uni-app-doc/7fbf2e60-60eb-4cb6-b6b1-4335f6f24d1a.jpg',
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            return response()->json($result);
        } catch (\Exception $e) {
            // 记录错误
            \Log::error('获取背景音乐列表失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());

            // 返回默认音乐
            return response()->json([
                [
                    'id' => 0,
                    'title' => '默认背景音乐',
                    'music_url' => 'https://bjetxgzv.cdn.bspapp.com/VKCEYUGU-uni-app-doc/a4b0600d-5e1f-4946-a280-c8975c2d5556.mp3',
                    'cover_image' => 'https://bjetxgzv.cdn.bspapp.com/VKCEYUGU-uni-app-doc/7fbf2e60-60eb-4cb6-b6b1-4335f6f24d1a.jpg',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            ]);
        }
    }

    /**
     * 获取背景音乐详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            // 尝试查找指定ID的音乐
            $music = BGM::enable()->find($id);

            // 如果没有找到指定ID的音乐
            if (!$music) {
                // 尝试获取第一个启用的音乐
                $music = BGM::enable()->first();

                // 如果仍然没有找到任何音乐，返回404
                if (!$music) {
                    return response()->json(['message' => '背景音乐不存在'], 404);
                }
            }

            // 处理音乐URL
            $musicUrl = $music->url;

            // 如果URL是相对路径（如 bgm/20231207/073172bd51a85dbffda8c991d05238e3.mp3），
            // 则直接使用，不需要转换为完整URL
            if ($musicUrl && !filter_var($musicUrl, FILTER_VALIDATE_URL) && !str_starts_with($musicUrl, '/')) {
                // 保持原样，不做处理
            } else {
                // 否则使用asset2url函数转换
                $musicUrl = asset2url($music->url);

                // 如果URL不包含文件扩展名，添加.mp3扩展名
                if ($musicUrl && !preg_match('/\.(mp3|wav|ogg|aac|m4a)(\?|$)/i', $musicUrl)) {
                    $musicUrl = preg_replace('/(\?|$)/', '.mp3$1', $musicUrl);
                }
            }

            // 如果URL仍然无效，使用默认音乐URL
            if (!$musicUrl) {
                $musicUrl = 'https://bjetxgzv.cdn.bspapp.com/VKCEYUGU-uni-app-doc/a4b0600d-5e1f-4946-a280-c8975c2d5556.mp3';
            }

            return response()->json([
                'id' => $music->id,
                'title' => $music->name,
                'music_url' => $musicUrl,
                'cover_image' => asset2url($music->img) ?: 'https://bjetxgzv.cdn.bspapp.com/VKCEYUGU-uni-app-doc/7fbf2e60-60eb-4cb6-b6b1-4335f6f24d1a.jpg',
                'created_at' => $music->created_at,
                'updated_at' => $music->updated_at,
            ]);
        } catch (\Exception $e) {
            return response()->json(['message' => '获取背景音乐详情失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 设置用户的背景音乐
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function setUserMusic(Request $request)
    {
        try {
            // 放宽验证规则，允许 music_id 为 0
            $request->validate([
                'music_id' => 'required',
            ]);

            $user = Auth::user();

            // 如果 music_id 是 0，可能是前端的默认音乐
            if ($request->music_id == 0) {
                // 获取第一个可用的音乐
                $music = BGM::enable()->first();

                if (!$music) {
                    return response()->json(['message' => '没有可用的背景音乐'], 404);
                }

                $user->bgm_id = $music->id;
            } else {
                // 检查音乐是否存在并启用
                $music = BGM::enable()->find($request->music_id);

                if (!$music) {
                    // 尝试获取第一个可用的音乐
                    $music = BGM::enable()->first();

                    if (!$music) {
                        return response()->json(['message' => '背景音乐不存在或未启用'], 404);
                    }
                }

                // 更新用户的背景音乐设置
                $user->bgm_id = $music->id;
            }

            $user->save();

            return response()->json(['message' => '设置背景音乐成功']);
        } catch (\Exception $e) {
            // 记录详细错误信息到日志
            \Log::error('设置背景音乐失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return response()->json(['message' => '设置背景音乐失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 获取用户当前的背景音乐
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserMusic(Request $request)
    {
        try {
            // 检查用户是否已登录
            if (!Auth::check()) {
                return response()->json(null);
            }

            $user = Auth::user();

            // 如果用户没有设置背景音乐，返回 null
            if (!$user->bgm_id) {
                return response()->json(null);
            }

            // 查找用户设置的背景音乐
            $music = BGM::enable()->find($user->bgm_id);

            // 如果找不到用户设置的音乐，返回 null
            if (!$music) {
                return response()->json(null);
            }

            // 处理音乐URL
            $musicUrl = $music->url;
            if ($musicUrl) {
                // 如果是相对路径，直接使用
                if (!filter_var($musicUrl, FILTER_VALIDATE_URL) && !str_starts_with($musicUrl, '/')) {
                    // 保持原样
                } else {
                    // 否则转换为完整URL
                    $musicUrl = asset2url($music->url);
                }
            }

            // 处理封面图片URL
            $coverUrl = asset2url($music->img);
            if (!$coverUrl) {
                $coverUrl = 'https://bjetxgzv.cdn.bspapp.com/VKCEYUGU-uni-app-doc/7fbf2e60-60eb-4cb6-b6b1-4335f6f24d1a.jpg';
            }

            return response()->json([
                'id' => $music->id,
                'name' => $music->name,
                'url' => $musicUrl,
                'cover' => $coverUrl,
                'created_at' => $music->created_at,
                'updated_at' => $music->updated_at,
            ]);
        } catch (\Exception $e) {
            \Log::error('获取用户背景音乐失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return response()->json(['message' => '获取用户背景音乐失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 获取所有背景音乐列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bgmList(Request $request)
    {
        try {
            // 获取所有背景音乐
            $musics = BGM::query()->get();

            // 转换为前端需要的格式
            $result = $musics->map(function ($music) {
                // 处理音乐URL
                $musicUrl = $music->url;
                if ($musicUrl) {
                    // 如果是相对路径，直接使用
                    if (!filter_var($musicUrl, FILTER_VALIDATE_URL) && !str_starts_with($musicUrl, '/')) {
                        // 保持原样
                    } else {
                        // 否则转换为完整URL
                        $musicUrl = asset2url($music->url);
                    }
                }

                return [
                    'id' => $music->id,
                    'name' => $music->name,
                    'url' => $musicUrl,
                    'cover' => asset2url($music->img),
                    'created_at' => $music->created_at,
                    'updated_at' => $music->updated_at,
                ];
            });

            return response()->json($result);
        } catch (\Exception $e) {
            \Log::error('获取背景音乐列表失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return response()->json(['message' => '获取背景音乐列表失败: ' . $e->getMessage()], 500);
        }
    }
}
