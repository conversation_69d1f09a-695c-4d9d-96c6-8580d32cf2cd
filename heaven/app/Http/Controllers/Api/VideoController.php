<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Video;
use App\Models\User;
use App\Models\User\UserMoney;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;
use App\AI\Models\AiModel;
use Illuminate\Support\Str;

class VideoController extends Controller
{
    /**
     * 获取用户视频列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();

            // 查询用户的所有视频
            $videos = Video::where('customer_id', $user->id)
                ->where('is_active', true)
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'total' => $videos->count(),
                'items' => $videos->map(function ($video) {
                    return [
                        'id' => $video->id,
                        'task_id' => $video->task_id,
                        'cover_image_url' => $video->cover_image_url,
                        'video_url' => $video->video_url,
                        'status' => $video->status,
                        'created_at' => $video->created_at,
                        'customer_id' => $video->customer_id,
                        'prompt' => $video->prompt,
                    ];
                })
            ]);
        } catch (\Exception $e) {
            Log::channel('daily')->error('获取视频列表失败: ' . $e->getMessage());
            return response()->json(['message' => '获取视频列表失败'], 500);
        }
    }

    /**
     * 生成视频
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function generate(Request $request)
    {
        try {
            // 验证请求
            $request->validate([
                'image' => 'required|file|image|max:10240', // 最大10MB
                'description' => 'required|string|max:1000',
                'cost' => 'required|numeric|min:0',
                'product_id' => 'required|integer|exists:products,id',
            ]);

            $user = Auth::user();

            // 检查用户余额
            $money = UserMoney::where('user_id', $user->id)->first();
            if (!$money || $money->money < $request->cost) {
                return response()->json(['message' => '余额不足'], 400);
            }

            // 上传图片到对象存储
            $image = $request->file('image');
            $imagePath = $image->store('videos/covers', 'cos');
            $coverImageUrl = Storage::disk('cos')->url($imagePath);

            // 记录请求参数
            Log::channel('daily')->info('H5前端请求生成视频', [
                'user_id' => $user->id,
                'description' => $request->description,
                'cost' => $request->cost,
                'product_id' => $request->product_id,
                'image_url' => $coverImageUrl
            ]);

            // 使用 VideoGenerationService 创建视频任务
            $service = new \App\AI\Services\VideoGenerationService();
            $result = $service->createVideoTask($coverImageUrl, $request->description, $user->id);

            // 扣除用户余额
            $money->money = max(0, $money->money - (int)$request->cost);
            $money->save();

            // 记录创建结果
            Log::channel('daily')->info('视频任务创建结果', [
                'result' => $result
            ]);

            return response()->json([
                'id' => $result['video_id'],
                'task_id' => $result['task_id'],
                'cover_image_url' => $coverImageUrl,
                'status' => 'pending',
                'created_at' => now(),
                'prompt' => $request->description
            ]);
        } catch (\Exception $e) {
            Log::channel('daily')->error('生成视频失败: ' . $e->getMessage());
            return response()->json(['message' => '生成视频失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 获取视频详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $user = Auth::user();

            $video = Video::where('id', $id)
                ->where('customer_id', $user->id)
                ->where('is_active', true)
                ->first();

            if (!$video) {
                return response()->json(['message' => '视频不存在'], 404);
            }

            return response()->json([
                'id' => $video->id,
                'task_id' => $video->task_id,
                'cover_image_url' => $video->cover_image_url,
                'video_url' => $video->video_url,
                'status' => $video->status,
                'created_at' => $video->created_at,
                'customer_id' => $video->customer_id,
                'prompt' => $video->prompt,
            ]);
        } catch (\Exception $e) {
            Log::channel('daily')->error('获取视频详情失败: ' . $e->getMessage());
            return response()->json(['message' => '获取视频详情失败'], 500);
        }
    }

    /**
     * 删除视频
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $user = Auth::user();

            $video = Video::where('id', $id)
                ->where('customer_id', $user->id)
                ->where('is_active', true)
                ->first();

            if (!$video) {
                return response()->json(['message' => '视频不存在'], 404);
            }

            $video->is_active = false;
            $video->save();

            return response()->json(['message' => '删除成功']);
        } catch (\Exception $e) {
            Log::channel('daily')->error('删除视频失败: ' . $e->getMessage());
            return response()->json(['message' => '删除视频失败'], 500);
        }
    }

    /**
     * 重新生成视频
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function regenerate(Request $request, $id)
    {
        try {
            $request->validate([
                'cost' => 'required|numeric|min:0',
            ]);

            $user = Auth::user();

            // 获取原始视频
            $originalVideo = Video::where('id', $id)
                ->where('customer_id', $user->id)
                ->where('is_active', true)
                ->first();

            if (!$originalVideo) {
                return response()->json(['message' => '视频不存在'], 404);
            }

            // 检查用户余额
            $money = UserMoney::where('user_id', $user->id)->first();
            if (!$money || $money->money < $request->cost) {
                return response()->json(['message' => '余额不足'], 400);
            }

            // 获取原始视频的图片地址和提示词
            $coverImageUrl = $originalVideo->cover_image_url;
            $prompt = $originalVideo->prompt;

            if (!$coverImageUrl) {
                return response()->json(['message' => '原始视频缺少图片地址'], 400);
            }

            // 创建新的视频记录
            $newVideo = new Video();
            $newVideo->customer_id = $user->id;
            $newVideo->cover_image_url = $coverImageUrl;
            $newVideo->status = 'pending';
            $newVideo->is_active = true;
            $newVideo->prompt = $prompt;
            $newVideo->save();

            // 扣除用户余额
            $money->money = max(0, $money->money - (int)$request->cost);
            $money->save();

            // 调用视频生成服务
            $result = $this->createVideoTask($coverImageUrl, $prompt);

            // 获取task_id
            $taskId = $result['task_id'] ?? ($result['data']['task_id'] ?? null);
            if (!$taskId) {
                throw new \Exception('创建视频任务失败：缺少task_id');
            }

            // 更新视频记录的task_id
            $newVideo->task_id = $taskId;
            $newVideo->create_result = json_encode($result);
            $newVideo->save();

            return response()->json([
                'id' => $newVideo->id,
                'task_id' => $newVideo->task_id,
                'cover_image_url' => $newVideo->cover_image_url,
                'status' => $newVideo->status,
                'created_at' => $newVideo->created_at,
                'prompt' => $newVideo->prompt
            ]);
        } catch (\Exception $e) {
            Log::channel('daily')->error('重新生成视频失败: ' . $e->getMessage());
            return response()->json(['message' => '重新生成视频失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 创建视频生成任务
     *
     * @param string $imageUrl
     * @param string $prompt
     * @param string $duration
     * @param float $cfgScale
     * @return array
     * @throws \Exception
     */
    private function createVideoTask($imageUrl, $prompt, $duration = '5', $cfgScale = 0.5)
    {
        // 获取启用的模型配置
        $modelConfig = AiModel::enabled()->first();
        if (!$modelConfig) {
            throw new \Exception('未找到启用状态的模型配置信息');
        }

        // 记录模型配置信息
        Log::channel('daily')->info('使用的模型配置: ' . json_encode([
            'model_name' => $modelConfig->model_name,
            'ai_model_name' => $modelConfig->ai_model_name,
            'domain' => $modelConfig->domain,
            'video_mode' => $modelConfig->video_mode,
            'callback_url' => $modelConfig->callback_url,
        ]));

        // 根据模型名称选择合适的处理器
        if ($modelConfig->model_name == '火山引擎') {
            return $this->createVolcanoEngineVideoTask($modelConfig, $imageUrl, $prompt, $duration, $cfgScale);
        } elseif ($modelConfig->model_name == '智谱清言') {
            return $this->createZhipuAIVideoTask($modelConfig, $imageUrl, $prompt, $duration, $cfgScale);
        } elseif ($modelConfig->model_name == '可灵AI') {
            return $this->createKelingAIVideoTask($modelConfig, $imageUrl, $prompt, $duration, $cfgScale);
        } else {
            throw new \Exception('不支持的模型类型: ' . $modelConfig->model_name);
        }
    }

    /**
     * 创建火山引擎视频生成任务
     *
     * @param ModelConfig $config
     * @param string $imageUrl
     * @param string $prompt
     * @param string $duration
     * @param float $cfgScale
     * @return array
     * @throws \Exception
     */
    private function createVolcanoEngineVideoTask($config, $imageUrl, $prompt, $duration, $cfgScale)
    {
        try {
            Log::channel('daily')->info('使用火山引擎模型处理视频生成任务');

            // 准备请求参数
            $data = [
                'model' => $config->ai_model_name, // 添加模型参数
                'content' => [
                    'image_url' => $imageUrl,
                    'prompt' => $prompt,
                    'duration' => (int)$duration,
                    'cfg_scale' => $cfgScale
                ]
            ];

            // 记录请求参数
            Log::channel('daily')->info('火山引擎请求参数: ' . json_encode($data));

            // 添加回调URL
            if (!empty($config->callback_url)) {
                $data['callback_url'] = $config->callback_url;
                Log::channel('daily')->info('使用配置中的回调URL: ' . $config->callback_url);
            }

            // 发送请求
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $config->access_key,
                'Content-Type' => 'application/json'
            ])->post($config->domain . '/contents/generations/tasks', $data);

            // 检查响应
            if ($response->successful()) {
                $result = $response->json();
                Log::channel('daily')->info('火山引擎视频生成任务创建成功: ' . json_encode($result));

                // 从响应中提取task_id
                $taskId = $result['task_id'] ?? null;
                if (!$taskId) {
                    throw new \Exception('火山引擎返回结果缺少task_id: ' . json_encode($result));
                }

                return [
                    'task_id' => $taskId,
                    'data' => $result
                ];
            } else {
                $errorMessage = '火山引擎API请求失败: ' . $response->status() . ' - ' . $response->body();
                Log::channel('daily')->error($errorMessage);
                throw new \Exception($errorMessage);
            }
        } catch (\Exception $e) {
            Log::channel('daily')->error('创建火山引擎视频任务失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 创建智谱清言视频生成任务
     *
     * @param ModelConfig $config
     * @param string $imageUrl
     * @param string $prompt
     * @param string $duration
     * @param float $cfgScale
     * @return array
     * @throws \Exception
     */
    private function createZhipuAIVideoTask($config, $imageUrl, $prompt, $duration, $cfgScale)
    {
        try {
            Log::channel('daily')->info('使用智谱清言模型处理视频生成任务');

            // 准备请求参数
            $data = [
                'model' => $config->ai_model_name,
                'image_url' => $imageUrl,
                'prompt' => $prompt,
                'quality' => 'quality' // 质量优先
            ];

            // 生成签名
            $timestamp = time();
            $nonce = Str::random(16);
            $signature = $this->generateZhipuSignature($config->access_key, $config->secret_key, $timestamp, $nonce);

            // 发送请求
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $signature,
                'Content-Type' => 'application/json'
            ])->post($config->domain . '/videos/generations', $data);

            // 检查响应
            if ($response->successful()) {
                $result = $response->json();
                Log::channel('daily')->info('智谱清言视频生成任务创建成功: ' . json_encode($result));

                // 从响应中提取task_id
                $taskId = $result['id'] ?? null;
                if (!$taskId) {
                    throw new \Exception('智谱清言返回结果缺少id: ' . json_encode($result));
                }

                return [
                    'task_id' => $taskId,
                    'data' => $result
                ];
            } else {
                $errorMessage = '智谱清言API请求失败: ' . $response->status() . ' - ' . $response->body();
                Log::channel('daily')->error($errorMessage);
                throw new \Exception($errorMessage);
            }
        } catch (\Exception $e) {
            Log::channel('daily')->error('创建智谱清言视频任务失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 生成智谱清言API签名
     *
     * @param string $apiKey
     * @param string $secretKey
     * @param int $timestamp
     * @param string $nonce
     * @return string
     */
    private function generateZhipuSignature($apiKey, $secretKey, $timestamp, $nonce)
    {
        $payload = [
            'api_key' => $apiKey,
            'timestamp' => $timestamp,
            'exp' => $timestamp + 3600, // 1小时过期
            'nonce' => $nonce
        ];

        $header = [
            'alg' => 'HS256',
            'typ' => 'JWT'
        ];

        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode(json_encode($header)));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode(json_encode($payload)));

        $signature = hash_hmac('sha256', $base64Header . '.' . $base64Payload, $secretKey, true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

        return $base64Header . '.' . $base64Payload . '.' . $base64Signature;
    }

    /**
     * 创建可灵AI视频生成任务
     *
     * @param ModelConfig $config
     * @param string $imageUrl
     * @param string $prompt
     * @param string $duration
     * @param float $cfgScale
     * @return array
     * @throws \Exception
     */
    private function createKelingAIVideoTask($config, $imageUrl, $prompt, $duration, $cfgScale)
    {
        try {
            Log::channel('daily')->info('使用可灵AI模型处理视频生成任务');

            // 准备请求参数
            $data = [
                'model_name' => $config->ai_model_name,
                'mode' => $config->video_mode,
                'duration' => $duration,
                'image' => $imageUrl,
                'prompt' => $prompt,
                'cfg_scale' => $cfgScale
            ];

            // 添加回调URL
            if (!empty($config->callback_url)) {
                $data['callback_url'] = $config->callback_url;
                Log::channel('daily')->info('使用配置中的回调URL: ' . $config->callback_url);
            }

            // 发送请求
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $config->access_key,
                'Content-Type' => 'application/json'
            ])->post($config->domain . '/api/v1/video/generate', $data);

            // 检查响应
            if ($response->successful()) {
                $result = $response->json();
                Log::channel('daily')->info('可灵AI视频生成任务创建成功: ' . json_encode($result));

                // 从响应中提取task_id
                $taskId = $result['task_id'] ?? null;
                if (!$taskId) {
                    throw new \Exception('可灵AI返回结果缺少task_id: ' . json_encode($result));
                }

                return [
                    'task_id' => $taskId,
                    'data' => $result
                ];
            } else {
                $errorMessage = '可灵AI API请求失败: ' . $response->status() . ' - ' . $response->body();
                Log::channel('daily')->error($errorMessage);
                throw new \Exception($errorMessage);
            }
        } catch (\Exception $e) {
            Log::channel('daily')->error('创建可灵AI视频任务失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 查询视频生成任务状态
     *
     * @param string $taskId
     * @return array
     * @throws \Exception
     */
    public function queryTaskStatus($taskId)
    {
        try {
            // 获取启用的模型配置
            $modelConfig = AiModel::enabled()->first();
            if (!$modelConfig) {
                throw new \Exception('未找到启用状态的模型配置信息');
            }

            // 根据模型名称选择合适的处理器
            if ($modelConfig->model_name == '火山引擎') {
                return $this->queryVolcanoEngineTaskStatus($modelConfig, $taskId);
            } elseif ($modelConfig->model_name == '智谱清言') {
                return $this->queryZhipuAITaskStatus($modelConfig, $taskId);
            } elseif ($modelConfig->model_name == '可灵AI') {
                return $this->queryKelingAITaskStatus($modelConfig, $taskId);
            } else {
                throw new \Exception('不支持的模型类型: ' . $modelConfig->model_name);
            }
        } catch (\Exception $e) {
            Log::channel('daily')->error('查询视频生成任务状态失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 查询火山引擎视频生成任务状态
     *
     * @param ModelConfig $config
     * @param string $taskId
     * @return array
     * @throws \Exception
     */
    private function queryVolcanoEngineTaskStatus($config, $taskId)
    {
        try {
            Log::channel('daily')->info('查询火山引擎视频生成任务状态 - task_id: ' . $taskId);

            // 发送请求
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $config->access_key,
                'Content-Type' => 'application/json'
            ])->get($config->domain . '/contents/generations/tasks/' . $taskId);

            // 检查响应
            if ($response->successful()) {
                $result = $response->json();
                Log::channel('daily')->info('查询火山引擎视频生成任务状态成功: ' . json_encode($result));

                // 解析状态
                $status = $result['status'] ?? 'UNKNOWN';
                $videoUrl = null;

                // 如果任务已完成，获取视频URL
                if ($status === 'SUCCEEDED') {
                    $videoUrl = $result['result']['video_url'] ?? null;
                    if (!$videoUrl) {
                        Log::channel('daily')->warning('火山引擎任务已完成但缺少视频URL: ' . json_encode($result));
                    }
                }

                return [
                    'status' => $status,
                    'video_url' => $videoUrl,
                    'data' => $result
                ];
            } else {
                $errorMessage = '查询火山引擎任务状态失败: ' . $response->status() . ' - ' . $response->body();
                Log::channel('daily')->error($errorMessage);
                throw new \Exception($errorMessage);
            }
        } catch (\Exception $e) {
            Log::channel('daily')->error('查询火山引擎任务状态失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 查询智谱清言视频生成任务状态
     *
     * @param ModelConfig $config
     * @param string $taskId
     * @return array
     * @throws \Exception
     */
    private function queryZhipuAITaskStatus($config, $taskId)
    {
        try {
            Log::channel('daily')->info('查询智谱清言视频生成任务状态 - task_id: ' . $taskId);

            // 生成签名
            $timestamp = time();
            $nonce = Str::random(16);
            $signature = $this->generateZhipuSignature($config->access_key, $config->secret_key, $timestamp, $nonce);

            // 发送请求
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $signature,
                'Content-Type' => 'application/json'
            ])->get($config->domain . '/videos/' . $taskId);

            // 检查响应
            if ($response->successful()) {
                $result = $response->json();
                Log::channel('daily')->info('查询智谱清言视频生成任务状态成功: ' . json_encode($result));

                // 解析状态
                $status = $result['task_status'] ?? 'UNKNOWN';
                $videoUrl = null;

                // 如果任务已完成，获取视频URL
                if ($status === 'SUCCESS') {
                    $videoResults = $result['video_result'] ?? [];
                    if (!empty($videoResults) && isset($videoResults[0]['url'])) {
                        $videoUrl = $videoResults[0]['url'];
                    } else {
                        Log::channel('daily')->warning('智谱清言任务已完成但缺少视频URL: ' . json_encode($result));
                    }
                }

                return [
                    'status' => $status,
                    'video_url' => $videoUrl,
                    'data' => $result
                ];
            } else {
                $errorMessage = '查询智谱清言任务状态失败: ' . $response->status() . ' - ' . $response->body();
                Log::channel('daily')->error($errorMessage);
                throw new \Exception($errorMessage);
            }
        } catch (\Exception $e) {
            Log::channel('daily')->error('查询智谱清言任务状态失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 查询可灵AI视频生成任务状态
     *
     * @param ModelConfig $config
     * @param string $taskId
     * @return array
     * @throws \Exception
     */
    private function queryKelingAITaskStatus($config, $taskId)
    {
        try {
            Log::channel('daily')->info('查询可灵AI视频生成任务状态 - task_id: ' . $taskId);

            // 发送请求
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $config->access_key,
                'Content-Type' => 'application/json'
            ])->get($config->domain . '/api/v1/video/task/' . $taskId);

            // 检查响应
            if ($response->successful()) {
                $result = $response->json();
                Log::channel('daily')->info('查询可灵AI视频生成任务状态成功: ' . json_encode($result));

                // 解析状态
                $status = $result['status'] ?? 'UNKNOWN';
                $videoUrl = null;

                // 如果任务已完成，获取视频URL
                if ($status === 'completed') {
                    $videoUrl = $result['video_url'] ?? null;
                    if (!$videoUrl) {
                        Log::channel('daily')->warning('可灵AI任务已完成但缺少视频URL: ' . json_encode($result));
                    }
                }

                return [
                    'status' => $status,
                    'video_url' => $videoUrl,
                    'data' => $result
                ];
            } else {
                $errorMessage = '查询可灵AI任务状态失败: ' . $response->status() . ' - ' . $response->body();
                Log::channel('daily')->error($errorMessage);
                throw new \Exception($errorMessage);
            }
        } catch (\Exception $e) {
            Log::channel('daily')->error('查询可灵AI任务状态失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 检查并更新视频状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkStatus(Request $request, $id)
    {
        try {
            $user = Auth::user();

            // 获取视频记录
            $video = Video::where('id', $id)
                ->where('customer_id', $user->id)
                ->where('is_active', true)
                ->first();

            if (!$video) {
                return response()->json(['message' => '视频不存在'], 404);
            }

            // 如果视频已经成功或失败，直接返回
            if ($video->status === 'success' || $video->status === 'failed') {
                return response()->json([
                    'id' => $video->id,
                    'task_id' => $video->task_id,
                    'cover_image_url' => $video->cover_image_url,
                    'video_url' => $video->video_url,
                    'status' => $video->status,
                    'created_at' => $video->created_at,
                    'customer_id' => $video->customer_id,
                    'prompt' => $video->prompt,
                ]);
            }

            // 如果没有task_id，无法查询状态
            if (!$video->task_id) {
                return response()->json(['message' => '视频任务ID不存在'], 400);
            }

            // 查询任务状态
            $result = $this->queryTaskStatus($video->task_id);

            // 更新视频状态
            $status = $result['status'];
            $videoUrl = $result['video_url'];

            // 根据不同模型的状态值进行映射
            if ($status === 'SUCCEEDED' || $status === 'SUCCESS' || $status === 'completed') {
                $video->status = 'success';
                if ($videoUrl) {
                    $video->video_url = $videoUrl;
                }
            } elseif ($status === 'FAILED' || $status === 'FAIL' || $status === 'failed') {
                $video->status = 'failed';
            } else {
                $video->status = 'pending';
            }

            $video->task_result = json_encode($result);
            $video->save();

            return response()->json([
                'id' => $video->id,
                'task_id' => $video->task_id,
                'cover_image_url' => $video->cover_image_url,
                'video_url' => $video->video_url,
                'status' => $video->status,
                'created_at' => $video->created_at,
                'customer_id' => $video->customer_id,
                'prompt' => $video->prompt,
            ]);
        } catch (\Exception $e) {
            Log::channel('daily')->error('检查视频状态失败: ' . $e->getMessage());
            return response()->json(['message' => '检查视频状态失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 处理回调请求
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handleCallback(Request $request)
    {
        try {
            Log::channel('daily')->info('收到视频生成回调: ' . json_encode($request->all()));

            // 获取任务ID
            $taskId = $request->input('task_id');
            if (!$taskId) {
                $taskId = $request->input('id');
            }

            if (!$taskId) {
                Log::channel('daily')->error('回调缺少任务ID: ' . json_encode($request->all()));
                return response()->json(['message' => '缺少任务ID'], 400);
            }

            // 查找对应的视频记录
            $video = Video::where('task_id', $taskId)
                ->where('is_active', true)
                ->first();

            if (!$video) {
                Log::channel('daily')->error('未找到对应的视频记录: ' . $taskId);
                return response()->json(['message' => '未找到对应的视频记录'], 404);
            }

            // 解析回调数据
            $status = $request->input('status');
            $videoUrl = $request->input('video_url');

            // 如果没有直接提供状态，尝试从其他字段获取
            if (!$status) {
                $status = $request->input('task_status');
            }

            // 如果没有直接提供视频URL，尝试从其他字段获取
            if (!$videoUrl) {
                $result = $request->input('result');
                if ($result && isset($result['video_url'])) {
                    $videoUrl = $result['video_url'];
                }

                $videoResult = $request->input('video_result');
                if (!$videoUrl && $videoResult && is_array($videoResult) && !empty($videoResult)) {
                    $videoUrl = $videoResult[0]['url'] ?? null;
                }
            }

            // 根据不同模型的状态值进行映射
            if ($status === 'SUCCEEDED' || $status === 'SUCCESS' || $status === 'completed') {
                $video->status = 'success';
                if ($videoUrl) {
                    $video->video_url = $videoUrl;
                }
            } elseif ($status === 'FAILED' || $status === 'FAIL' || $status === 'failed') {
                $video->status = 'failed';
            } else {
                $video->status = 'pending';
            }

            $video->callback_result = json_encode($request->all());
            $video->save();

            return response()->json(['message' => '回调处理成功']);
        } catch (\Exception $e) {
            Log::channel('daily')->error('处理回调失败: ' . $e->getMessage());
            return response()->json(['message' => '处理回调失败: ' . $e->getMessage()], 500);
        }
    }
}
