#!/bin/bash

# Heaven Docker镜像构建脚本

set -e

# 配置
IMAGE_NAME="heaven-web-image"
IMAGE_TAG="latest"
EXPORT_NAME="heaven-web-optimized.tar"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要文件
check_files() {
    log_info "检查必要文件..."

    local required_files=(
        "Dockerfile.richarvey"
        "../docker-entrypoint.sh"
        "../nginx-default.conf"
        "../zzz-custom.conf"
        ".env.example"
        "public/index.php"
    )

    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "缺少必要文件: $file"
            exit 1
        fi
    done

    log_success "所有必要文件检查通过"
}

# 清理旧镜像
cleanup_old_images() {
    log_info "清理旧镜像..."

    # 删除旧的镜像
    docker rmi $IMAGE_NAME:$IMAGE_TAG 2>/dev/null || true

    # 清理悬空镜像
    docker image prune -f

    log_success "旧镜像清理完成"
}

# 构建镜像
build_image() {
    log_info "开始构建Docker镜像..."

    # 复制配置文件到当前目录
    cp ../nginx-default.conf .
    cp ../zzz-custom.conf .
    cp ../docker-entrypoint.sh .

    # 构建镜像
    docker build -f Dockerfile.richarvey -t $IMAGE_NAME:$IMAGE_TAG .

    # 清理临时文件
    rm -f nginx-default.conf zzz-custom.conf docker-entrypoint.sh

    if [ $? -eq 0 ]; then
        log_success "镜像构建成功: $IMAGE_NAME:$IMAGE_TAG"
    else
        log_error "镜像构建失败"
        exit 1
    fi
}

# 测试镜像
test_image() {
    log_info "测试镜像..."

    # 创建测试容器
    local test_container="heaven-test-$(date +%s)"

    docker run -d --name $test_container \
        -p 8082:80 \
        $IMAGE_NAME:$IMAGE_TAG

    # 等待容器启动
    sleep 15

    # 测试HTTP响应
    local response=$(docker exec $test_container curl -s -o /dev/null -w "%{http_code}" http://localhost 2>/dev/null || echo "000")

    # 清理测试容器
    docker stop $test_container >/dev/null 2>&1
    docker rm $test_container >/dev/null 2>&1

    if [ "$response" = "200" ] || [ "$response" = "302" ]; then
        log_success "镜像测试通过 (HTTP $response)"
    else
        log_warning "镜像测试异常 (HTTP $response)，但可能是正常的Laravel响应"
    fi
}

# 导出镜像
export_image() {
    log_info "导出镜像到文件..."

    docker save -o $EXPORT_NAME $IMAGE_NAME:$IMAGE_TAG

    if [ -f "$EXPORT_NAME" ]; then
        local size=$(du -h "$EXPORT_NAME" | cut -f1)
        log_success "镜像导出成功: $EXPORT_NAME ($size)"
    else
        log_error "镜像导出失败"
        exit 1
    fi
}

# 显示镜像信息
show_image_info() {
    log_info "镜像信息:"
    docker images $IMAGE_NAME:$IMAGE_TAG

    echo ""
    log_info "导出文件:"
    ls -lh $EXPORT_NAME 2>/dev/null || echo "未找到导出文件"
}

# 主函数
main() {
    echo "🚀 Heaven Docker镜像构建工具"
    echo "时间: $(date)"
    echo ""

    check_files
    cleanup_old_images
    build_image
    test_image
    export_image
    show_image_info

    echo ""
    log_success "构建完成！"
    echo ""
    echo "📋 使用说明:"
    echo "1. 上传镜像文件到服务器:"
    echo "   scp $EXPORT_NAME root@82.156.98.152:/tmp/"
    echo ""
    echo "2. 在服务器上导入镜像:"
    echo "   docker load -i /tmp/$EXPORT_NAME"
    echo ""
    echo "3. 更新docker-compose.yml使用新镜像:"
    echo "   image: $IMAGE_NAME:$IMAGE_TAG"
    echo ""
    echo "4. 重启服务:"
    echo "   docker-compose down && docker-compose up -d"
}

# 执行主函数
main "$@"
