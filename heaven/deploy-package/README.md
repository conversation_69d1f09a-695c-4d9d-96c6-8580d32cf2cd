# Heaven项目服务器部署包

## 📦 包含文件
- `heaven-web-image.tar.gz` - Docker镜像文件
- `heaven-project.tar.gz` - 项目源代码
- `docker-compose.server.yml` - 服务器部署配置
- `.env.production` - 生产环境配置
- `server-deploy.sh` - 完整部署脚本
- `quick-deploy.sh` - 快速部署脚本（仅更新代码）

## 🚀 部署步骤

### 首次部署
```bash
# 1. 上传整个deploy-package目录到服务器
scp -r deploy-package/ user@server:/path/to/deployment/

# 2. 登录服务器并执行部署
ssh user@server
cd /path/to/deployment/deploy-package/
chmod +x server-deploy.sh
./server-deploy.sh
```

### 快速更新（仅更新代码）
```bash
# 如果只是代码更新，使用快速部署
./quick-deploy.sh
```

## 🔧 部署后配置

1. **更新域名配置**
   编辑 `.env.production` 文件中的域名设置

2. **访问应用**
   - 后台管理: `http://server-ip:8081/admin`
   - 前台页面: `http://server-ip:8081`

## ⚠️ 注意事项

- 确保服务器已安装 Docker 和 Docker Compose
- 确保端口 8081 和 6379 可用
- 确保MySQL数据库可访问
- 建议在生产环境中配置SSL证书
