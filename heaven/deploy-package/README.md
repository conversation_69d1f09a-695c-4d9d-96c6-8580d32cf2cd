# Heaven项目服务器部署包

## 📦 包含文件
- `heaven-web-image.tar.gz` - Docker镜像文件 (345MB)
- `heaven-project.tar.gz` - 项目源代码 (239MB)
- `docker-compose.server.yml` - 服务器部署配置
- `server-deploy.sh` - 完整部署脚本
- `quick-deploy.sh` - 快速部署脚本（仅更新代码）

## 🚀 部署步骤

### 首次部署
```bash
# 1. 上传整个deploy-package目录到服务器
scp -r deploy-package/ user@server:/path/to/deployment/

# 2. 登录服务器并执行部署
ssh user@server
cd /path/to/deployment/deploy-package/
chmod +x server-deploy.sh
./server-deploy.sh
```

### 快速更新（仅更新代码）
```bash
# 如果只是代码更新，使用快速部署
chmod +x quick-deploy.sh
./quick-deploy.sh
```

## 🔧 部署后配置

1. **更新域名配置**
   编辑容器内的 `.env` 文件中的域名设置

2. **访问应用**
   - 后台管理: `http://server-ip:8081/admin`
   - 前台页面: `http://server-ip:8081`

## ⚠️ 注意事项

- 确保服务器已安装 Docker 和 Docker Compose
- 确保端口 8081 和 6379 可用
- 确保MySQL数据库可访问
- 建议在生产环境中配置SSL证书

## 🔧 环境变量配置

在服务器上，你可能需要修改以下环境变量：
- `SASS_CENTRAL_DOMAINS` - 添加服务器域名
- `DB_HOST` - 数据库主机地址
- `DB_DATABASE` - 数据库名称
- `DB_USERNAME` - 数据库用户名
- `DB_PASSWORD` - 数据库密码

## 🎯 镜像信息

本部署包使用的是已修复所有问题的heaven-web:latest镜像，包括：
- ✅ Nginx配置正确（使用Unix socket连接PHP-FPM）
- ✅ Laravel路由正常工作
- ✅ 管理后台可正常访问
- ✅ 数据库和Redis连接正常
- ✅ 所有依赖已安装并优化

## 🔧 部署修复

本版本修复了以下部署问题：
- ✅ 项目文件正确解压到 `heaven/` 目录
- ✅ 修复tar解压时的macOS扩展属性警告
- ✅ 使用兼容的tar命令，支持Linux和macOS
- ✅ 使用 `COPYFILE_DISABLE=1` 环境变量避免macOS特定文件
- ✅ 错误输出重定向，避免显示警告信息
- ✅ 测试脚本验证解压功能正常
