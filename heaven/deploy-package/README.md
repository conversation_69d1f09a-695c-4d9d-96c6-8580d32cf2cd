# Heaven项目服务器部署包

## 📦 包含文件
- `heaven-web-image.tar.gz` - Docker镜像文件 (345MB)
- `heaven-project.tar.gz` - 项目源代码 (239MB)
- `docker-compose.server.yml` - 服务器部署配置
- `server-deploy.sh` - 完整部署脚本
- `quick-deploy.sh` - 快速部署脚本（仅更新代码）

## 🚀 部署步骤

### 首次部署
```bash
# 1. 上传整个deploy-package目录到服务器
scp -r deploy-package/ user@server:/path/to/deployment/

# 2. 登录服务器并执行部署
ssh user@server
cd /path/to/deployment/deploy-package/
chmod +x server-deploy.sh
./server-deploy.sh
```

### 快速更新（仅更新代码）
```bash
# 如果只是代码更新，使用快速部署
chmod +x quick-deploy.sh
./quick-deploy.sh
```

## 🔧 部署后配置

1. **更新域名配置**
   编辑容器内的 `.env` 文件中的域名设置

2. **访问应用**
   - 后台管理: `http://server-ip:8081/admin`
   - 前台页面: `http://server-ip:8081`

## ⚠️ 注意事项

- 确保服务器已安装 Docker 和 Docker Compose
- 确保端口 8081 和 6379 可用
- 确保MySQL数据库可访问
- 建议在生产环境中配置SSL证书

## 🔧 环境变量配置

在服务器上，你可能需要修改以下环境变量：
- `SASS_CENTRAL_DOMAINS` - 添加服务器域名
- `DB_HOST` - 数据库主机地址
- `DB_DATABASE` - 数据库名称
- `DB_USERNAME` - 数据库用户名
- `DB_PASSWORD` - 数据库密码

## 🎯 镜像信息

本部署包使用的是已修复所有问题的heaven-web:latest镜像，包括：
- ✅ Nginx配置正确（使用Unix socket连接PHP-FPM）
- ✅ Laravel路由正常工作
- ✅ 管理后台可正常访问
- ✅ 数据库和Redis连接正常
- ✅ 所有依赖已安装并优化

## 🔧 部署修复

本版本修复了以下部署问题：
- ✅ 项目文件正确解压到 `heaven/` 目录
- ✅ 修复tar解压时的macOS扩展属性警告
- ✅ 使用兼容的tar命令，支持Linux和macOS
- ✅ 使用 `COPYFILE_DISABLE=1` 环境变量避免macOS特定文件
- ✅ 错误输出重定向，避免显示警告信息
- ✅ 测试脚本验证解压功能正常

## 🔧 服务器配置

本部署包已针对服务器环境进行优化：
- ✅ 使用服务器本地的 `heaven-redis-image:latest` Redis镜像
- ✅ 避免从Docker Hub下载Redis镜像，解决网络超时问题
- ✅ 添加容器启动检查和自动重试机制
- ✅ 如果容器启动失败，会自动显示日志信息

## 🚨 故障排除

### heaven-web容器一直重启的解决方案

如果遇到 `heaven-web` 容器状态显示 `Restarting (1)` 而没有端口映射，请按以下步骤排查：

#### 1. 运行诊断脚本
```bash
./diagnose.sh
```

#### 2. 检查容器日志
```bash
docker logs heaven-web --tail 50
```

#### 3. 常见问题和解决方案

**问题1: 端口被占用**
```bash
# 检查端口占用
lsof -i :8081
# 如果有进程占用，停止该进程或更改端口
```

**问题2: 项目文件权限问题**
```bash
# 检查项目目录权限
ls -la heaven/
# 修复权限
sudo chown -R 1000:1000 heaven/
sudo chmod -R 755 heaven/
```

**问题3: 数据库连接失败**
```bash
# 检查数据库连接
mysql -h 82.156.98.152 -u yunyitang -p heaven
# 确保数据库服务正常运行
```

**问题4: 强制重建容器**
```bash
# 停止并删除容器
docker-compose -f docker-compose.server.yml down
# 重新启动
docker-compose -f docker-compose.server.yml up -d
```

#### 4. 手动启动容器进行调试
```bash
# 进入容器调试
docker run -it --rm heaven-web:latest /bin/bash
# 或者查看启动脚本
docker run --rm heaven-web:latest cat /docker-entrypoint.sh
```
