#!/bin/bash

echo "🔍 403错误诊断工具"
echo "=================="

echo ""
echo "1. 📋 检查Nginx访问日志："
docker exec heaven-web tail -10 /var/log/nginx/access.log 2>/dev/null || echo "无法读取访问日志"

echo ""
echo "2. 📋 检查Nginx错误日志："
docker exec heaven-web tail -10 /var/log/nginx/error.log 2>/dev/null || echo "无法读取错误日志"

echo ""
echo "3. 📁 检查网站根目录："
docker exec heaven-web ls -la /var/www/html/ | head -10

echo ""
echo "4. 📁 检查public目录："
docker exec heaven-web ls -la /var/www/html/public/ | head -10

echo ""
echo "5. 🔍 检查index.php文件："
docker exec heaven-web ls -la /var/www/html/public/index.php

echo ""
echo "6. 📋 检查Nginx配置文件："
docker exec heaven-web cat /etc/nginx/sites-available/default | grep -A 10 -B 5 "root\|index"

echo ""
echo "7. 🌐 容器内部测试访问："
docker exec heaven-web curl -I http://localhost/ 2>/dev/null || echo "容器内部访问失败"

echo ""
echo "8. 🔍 检查PHP-FPM进程："
docker exec heaven-web ps aux | grep php-fpm | head -3

echo ""
echo "9. 🔍 检查Nginx进程："
docker exec heaven-web ps aux | grep nginx

echo ""
echo "10. 📋 检查端口监听："
docker exec heaven-web netstat -tlnp | grep -E "(80|9000)"
