#!/bin/bash

echo "🔍 检查Docker容器内部服务状态"
echo "================================"

echo ""
echo "1. 📊 容器基本信息："
docker exec heaven-web hostname
docker exec heaven-web whoami
docker exec heaven-web pwd

echo ""
echo "2. 🔍 检查进程状态："
echo "Nginx进程："
docker exec heaven-web ps aux | grep nginx | grep -v grep
echo ""
echo "PHP-FPM进程："
docker exec heaven-web ps aux | grep php-fpm | grep -v grep | head -3

echo ""
echo "3. 🌐 检查端口监听："
docker exec heaven-web netstat -tlnp | grep -E "(80|9000|443)"

echo ""
echo "4. 📁 检查项目文件结构："
echo "项目根目录："
docker exec heaven-web ls -la /var/www/html/ | head -10
echo ""
echo "public目录："
docker exec heaven-web ls -la /var/www/html/public/ | head -5

echo ""
echo "5. 🔍 检查关键文件："
echo "index.php存在性："
docker exec heaven-web test -f /var/www/html/public/index.php && echo "✅ index.php存在" || echo "❌ index.php不存在"
echo ""
echo "index.php权限："
docker exec heaven-web ls -la /var/www/html/public/index.php

echo ""
echo "6. 🧪 容器内部HTTP测试："
echo "测试根路径："
docker exec heaven-web curl -s -o /dev/null -w "HTTP状态码: %{http_code}\n" http://localhost/
echo ""
echo "测试admin路径："
docker exec heaven-web curl -s -o /dev/null -w "HTTP状态码: %{http_code}\n" http://localhost/admin
echo ""
echo "获取响应头："
docker exec heaven-web curl -I http://localhost/ 2>/dev/null | head -5

echo ""
echo "7. 📋 检查Nginx配置："
echo "Nginx配置测试："
docker exec heaven-web nginx -t
echo ""
echo "Nginx配置文件关键部分："
docker exec heaven-web grep -A 5 -B 2 "root\|index\|location" /etc/nginx/sites-available/default

echo ""
echo "8. 🔍 检查PHP配置："
echo "PHP版本："
docker exec heaven-web php -v | head -1
echo ""
echo "PHP-FPM配置测试："
docker exec heaven-web php-fpm -t 2>/dev/null && echo "✅ PHP-FPM配置正常" || echo "❌ PHP-FPM配置有问题"

echo ""
echo "9. 📋 检查日志文件："
echo "Nginx访问日志（最后5行）："
docker exec heaven-web tail -5 /var/log/nginx/access.log 2>/dev/null || echo "无访问日志"
echo ""
echo "Nginx错误日志（最后5行）："
docker exec heaven-web tail -5 /var/log/nginx/error.log 2>/dev/null || echo "无错误日志"
echo ""
echo "PHP错误日志："
docker exec heaven-web tail -3 /var/log/php*.log 2>/dev/null || echo "无PHP错误日志"

echo ""
echo "10. 🧪 Laravel应用测试："
echo "Laravel artisan测试："
docker exec heaven-web php artisan --version 2>/dev/null || echo "❌ Laravel artisan无法运行"
echo ""
echo "检查.env文件："
docker exec heaven-web test -f /var/www/html/.env && echo "✅ .env文件存在" || echo "❌ .env文件不存在"

echo ""
echo "================================"
echo "🎯 诊断总结："
echo "如果看到HTTP状态码200，说明容器内部服务正常"
echo "如果看到HTTP状态码403/404/500，说明应用层有问题"
echo "如果无法连接，说明Nginx/PHP-FPM服务有问题"
