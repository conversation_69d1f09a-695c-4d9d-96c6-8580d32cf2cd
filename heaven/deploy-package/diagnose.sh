#!/bin/bash

echo "🔍 Heaven项目容器诊断工具"
echo "================================"

echo ""
echo "📊 容器状态："
docker ps -a | grep -E "(heaven-web|heaven-redis)"

echo ""
echo "🔌 端口映射检查："
docker ps | grep -E "(heaven-web|heaven-redis)" | grep -o "0.0.0.0:[0-9]*->[0-9]*"

echo ""
echo "🌐 端口监听检查："
netstat -tlnp | grep -E "(8081|6379)" || echo "未找到8081或6379端口监听"

echo ""
echo "📋 heaven-web容器日志（最后20行）："
docker logs heaven-web --tail 20 2>/dev/null || echo "无法获取heaven-web日志"

echo ""
echo "📋 heaven-redis容器日志（最后10行）："
docker logs heaven-redis --tail 10 2>/dev/null || echo "无法获取heaven-redis日志"

echo ""
echo "🔧 Docker Compose状态："
docker-compose -f docker-compose.server.yml ps

echo ""
echo "💾 磁盘空间检查："
df -h | grep -E "(/$|/var)"

echo ""
echo "🧠 内存使用："
free -h

echo ""
echo "🔍 如果heaven-web容器一直重启，常见原因："
echo "1. 端口8081被占用"
echo "2. 项目文件权限问题"
echo "3. 环境变量配置错误"
echo "4. 数据库连接失败"
echo "5. 内存不足"

echo ""
echo "🛠️ 建议的修复步骤："
echo "1. 检查端口占用: lsof -i :8081"
echo "2. 检查项目目录权限: ls -la heaven/"
echo "3. 重新启动容器: docker-compose -f docker-compose.server.yml restart web"
echo "4. 查看详细日志: docker logs heaven-web -f"
