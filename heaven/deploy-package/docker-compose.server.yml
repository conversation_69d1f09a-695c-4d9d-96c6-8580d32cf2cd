version: '3.8'

services:
  web:
    # 使用上传的镜像，不进行构建
    image: heaven-web:latest
    container_name: heaven-web
    restart: unless-stopped
    ports:
      - "8081:80"
    volumes:
      # 挂载整个项目目录
      - ./heaven:/var/www/html
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - APP_URL=http://*************:8081
      # 数据库配置 - 使用服务器本地MySQL
      - DB_CONNECTION=mysql
      - DB_HOST=*************
      - DB_PORT=3306
      - DB_DATABASE=heaven
      - DB_USERNAME=yunyitang
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      # 缓存配置
      - CACHE_DRIVER=redis
      - SESSION_DRIVER=redis
      - QUEUE_CONNECTION=redis
    depends_on:
      - redis
    networks:
      - heaven-network

  redis:
    image: heaven-redis-image:latest
    container_name: heaven-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - heaven-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      retries: 3
      timeout: 5s
      interval: 30s

volumes:
  redis_data:
    driver: local

networks:
  heaven-network:
    driver: bridge
