#!/bin/bash

echo "🔧 修复403 Forbidden错误..."

# 1. 检查容器状态
echo "🔍 检查容器状态..."
docker ps | grep heaven-web

# 2. 检查Nginx错误日志
echo "📋 检查Nginx错误日志..."
docker exec heaven-web tail -20 /var/log/nginx/error.log 2>/dev/null || echo "无法读取Nginx错误日志"

# 3. 检查项目目录权限
echo "📁 检查项目目录权限..."
docker exec heaven-web ls -la /var/www/html/

# 4. 检查public目录权限
echo "📁 检查public目录权限..."
docker exec heaven-web ls -la /var/www/html/public/

# 5. 修复目录权限
echo "🔐 修复目录权限..."
docker exec heaven-web chown -R nginx:nginx /var/www/html
docker exec heaven-web chmod -R 755 /var/www/html
docker exec heaven-web chmod -R 775 /var/www/html/storage
docker exec heaven-web chmod -R 775 /var/www/html/bootstrap/cache

# 6. 确保public目录可读
echo "🔐 确保public目录可读..."
docker exec heaven-web chmod 755 /var/www/html/public
docker exec heaven-web chmod 644 /var/www/html/public/index.php

# 7. 检查Nginx配置
echo "📋 检查Nginx配置..."
docker exec heaven-web nginx -t

# 8. 重启Nginx
echo "🔄 重启Nginx..."
docker exec heaven-web nginx -s reload

# 9. 检查PHP-FPM状态
echo "🔍 检查PHP-FPM状态..."
docker exec heaven-web ps aux | grep php-fpm

# 10. 测试访问
echo "🌐 测试访问..."
docker exec heaven-web curl -I http://localhost/

echo ""
echo "✅ 403错误修复完成！"
echo "🌐 请尝试访问: http://$(hostname -I | awk '{print $1}'):8081"
echo "🌐 管理后台: http://$(hostname -I | awk '{print $1}'):8081/admin"

echo ""
echo "🔍 如果仍然403，请检查："
echo "1. SELinux状态: getenforce"
echo "2. 防火墙设置: iptables -L"
echo "3. Nginx配置: docker exec heaven-web cat /etc/nginx/sites-available/default"
