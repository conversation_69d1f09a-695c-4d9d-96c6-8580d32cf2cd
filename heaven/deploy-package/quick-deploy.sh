#!/bin/bash

echo "⚡ 快速部署（仅更新代码）..."

# 检查项目文件
if [ ! -f "heaven-project.tar.gz" ]; then
    echo "❌ 项目文件不存在"
    exit 1
fi

# 1. 备份当前项目
echo "💾 备份当前项目..."
if [ -d "heaven-backup" ]; then
    rm -rf heaven-backup
fi
cp -r heaven heaven-backup 2>/dev/null || true

# 2. 解压新项目文件
echo "📦 解压新项目文件..."
tar -xzf heaven-project.tar.gz

# 3. 重启容器
echo "🔄 重启容器..."
docker-compose -f docker-compose.server.yml restart web

# 4. 清理缓存
echo "🧹 清理缓存..."
sleep 10
docker exec heaven-web php artisan config:cache
docker exec heaven-web php artisan route:cache
docker exec heaven-web php artisan view:cache

echo "✅ 快速部署完成！"
