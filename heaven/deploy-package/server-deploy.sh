#!/bin/bash

echo "🚀 在服务器上部署Heaven项目..."

# 加载镜像
echo "📦 加载Docker镜像..."
gunzip heaven-web-image.tar.gz
docker load -i heaven-web-image.tar

# 停止现有容器
echo "⏹️ 停止现有容器..."
docker-compose -f docker-compose.server.yml down

# 启动新容器
echo "▶️ 启动新容器..."
docker-compose -f docker-compose.server.yml up -d

# 等待容器启动
echo "⏳ 等待容器启动..."
sleep 30

# 检查容器状态
echo "🔍 检查容器状态..."
docker-compose -f docker-compose.server.yml ps

# 运行Laravel初始化命令
echo "🔧 运行Laravel初始化..."
docker exec heaven-web php artisan config:cache
docker exec heaven-web php artisan route:cache
docker exec heaven-web php artisan view:cache

echo "✅ 部署完成！"
echo "🌐 访问地址: http://your-server-ip:8081/admin"
