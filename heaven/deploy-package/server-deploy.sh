#!/bin/bash

echo "🚀 在服务器上部署Heaven项目..."

# 检查必要文件
if [ ! -f "heaven-web-image.tar.gz" ]; then
    echo "❌ 镜像文件不存在"
    exit 1
fi

if [ ! -f "heaven-project.tar.gz" ]; then
    echo "❌ 项目文件不存在"
    exit 1
fi

# 1. 解压项目文件到heaven目录
echo "📦 解压项目文件..."
mkdir -p heaven
tar -xzf heaven-project.tar.gz -C heaven 2>/dev/null || tar -xzf heaven-project.tar.gz -C heaven

# 2. 加载Docker镜像
echo "📦 加载Docker镜像..."
gunzip heaven-web-image.tar.gz
docker load -i heaven-web-image.tar

# 3. 停止现有容器
echo "⏹️ 停止现有容器..."
docker-compose -f docker-compose.server.yml down 2>/dev/null || true

# 4. 启动新容器（使用本地Redis镜像）
echo "▶️ 启动新容器..."
docker-compose -f docker-compose.server.yml up -d

# 5. 等待容器启动
echo "⏳ 等待容器启动..."
sleep 30

# 6. 检查容器状态
echo "🔍 检查容器状态..."
docker-compose -f docker-compose.server.yml ps

# 7. 检查heaven-web容器是否启动成功
echo "🔍 检查heaven-web容器状态..."
if ! docker ps | grep -q "heaven-web.*Up"; then
    echo "❌ heaven-web容器未正常运行，当前状态："
    docker ps -a | grep heaven-web

    echo "📋 查看容器日志："
    docker-compose -f docker-compose.server.yml logs web

    echo "🔄 尝试重新启动容器..."
    docker-compose -f docker-compose.server.yml stop web
    docker-compose -f docker-compose.server.yml rm -f web
    docker-compose -f docker-compose.server.yml up -d web

    sleep 20

    if ! docker ps | grep -q "heaven-web.*Up"; then
        echo "❌ 容器重启后仍然失败，最新日志："
        docker-compose -f docker-compose.server.yml logs web
        echo "❌ 部署失败，请检查上述日志信息"
        exit 1
    fi
    echo "✅ 容器重启成功"
else
    echo "✅ heaven-web容器运行正常"
fi

# 7. 运行Laravel初始化命令
echo "🔧 运行Laravel初始化..."
docker exec heaven-web php artisan config:cache
docker exec heaven-web php artisan route:cache
docker exec heaven-web php artisan view:cache

# 8. 设置存储目录权限
echo "🔐 设置存储目录权限..."
docker exec heaven-web chown -R nginx:nginx /var/www/html/storage
docker exec heaven-web chmod -R 775 /var/www/html/storage

echo "✅ 部署完成！"
echo "🌐 访问地址: http://$(hostname -I | awk '{print $1}'):8081/admin"
