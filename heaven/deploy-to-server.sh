#!/bin/bash

# Heaven项目服务器部署包生成脚本
# 使用方法: ./deploy-to-server.sh

echo "🚀 开始生成Heaven项目部署包..."

# 检查Docker容器是否运行
if ! docker ps | grep -q "heaven-web"; then
    echo "❌ heaven-web容器未运行，请先启动容器"
    echo "运行: docker-compose -f docker-compose.richarvey.yml up -d"
    exit 1
fi

# 1. 清理Laravel缓存
echo "🧹 清理Laravel缓存..."
docker exec heaven-web php artisan config:clear
docker exec heaven-web php artisan route:clear
docker exec heaven-web php artisan cache:clear
docker exec heaven-web php artisan view:clear

# 2. 重新构建最新镜像
echo "🔨 重新构建最新镜像..."
docker-compose -f docker-compose.richarvey.yml build web

# 3. 保存最新镜像
echo "📦 正在保存最新Docker镜像..."
docker save heaven-web:latest -o heaven-web-image.tar

# 4. 压缩镜像文件
echo "🗜️ 正在压缩镜像文件..."
gzip heaven-web-image.tar

# 5. 清理旧的部署包
echo "🗑️ 清理旧的部署包..."
rm -rf deploy-package

# 6. 创建部署包目录
echo "📁 创建部署包..."
mkdir -p deploy-package

# 7. 复制镜像文件
cp heaven-web-image.tar.gz deploy-package/

# 8. 复制配置文件
cp ../docker-compose.server.yml deploy-package/ 2>/dev/null || echo "⚠️ docker-compose.server.yml 不存在，跳过"
cp .env.production deploy-package/ 2>/dev/null || echo "⚠️ .env.production 不存在，跳过"

# 9. 打包heaven工程（排除不必要的文件）
echo "📦 打包heaven工程..."
tar --exclude='vendor' \
    --exclude='node_modules' \
    --exclude='storage/logs/*' \
    --exclude='storage/framework/cache/*' \
    --exclude='storage/framework/sessions/*' \
    --exclude='storage/framework/views/*' \
    --exclude='storage/debugbar/*' \
    --exclude='.git' \
    --exclude='deploy-package' \
    --exclude='heaven-web-image.tar*' \
    --exclude='*.log' \
    -czf deploy-package/heaven-project.tar.gz .

# 10. 创建服务器部署脚本
echo "📝 创建服务器部署脚本..."
cat > deploy-package/server-deploy.sh << 'EOF'
#!/bin/bash

echo "🚀 在服务器上部署Heaven项目..."

# 检查必要文件
if [ ! -f "heaven-web-image.tar.gz" ]; then
    echo "❌ 镜像文件不存在"
    exit 1
fi

if [ ! -f "heaven-project.tar.gz" ]; then
    echo "❌ 项目文件不存在"
    exit 1
fi

# 1. 解压项目文件
echo "📦 解压项目文件..."
tar -xzf heaven-project.tar.gz

# 2. 加载Docker镜像
echo "📦 加载Docker镜像..."
gunzip heaven-web-image.tar.gz
docker load -i heaven-web-image.tar

# 3. 停止现有容器
echo "⏹️ 停止现有容器..."
docker-compose -f docker-compose.server.yml down 2>/dev/null || true

# 4. 启动新容器
echo "▶️ 启动新容器..."
docker-compose -f docker-compose.server.yml up -d

# 5. 等待容器启动
echo "⏳ 等待容器启动..."
sleep 30

# 6. 检查容器状态
echo "🔍 检查容器状态..."
docker-compose -f docker-compose.server.yml ps

# 7. 运行Laravel初始化命令
echo "🔧 运行Laravel初始化..."
docker exec heaven-web php artisan config:cache
docker exec heaven-web php artisan route:cache
docker exec heaven-web php artisan view:cache

# 8. 设置存储目录权限
echo "🔐 设置存储目录权限..."
docker exec heaven-web chown -R www-data:www-data /var/www/html/storage
docker exec heaven-web chmod -R 775 /var/www/html/storage

echo "✅ 部署完成！"
echo "🌐 访问地址: http://$(hostname -I | awk '{print $1}'):8081/admin"
EOF

chmod +x deploy-package/server-deploy.sh

# 11. 创建快速部署脚本（仅更新代码，不重新构建镜像）
echo "📝 创建快速部署脚本..."
cat > deploy-package/quick-deploy.sh << 'EOF'
#!/bin/bash

echo "⚡ 快速部署（仅更新代码）..."

# 检查项目文件
if [ ! -f "heaven-project.tar.gz" ]; then
    echo "❌ 项目文件不存在"
    exit 1
fi

# 1. 备份当前项目
echo "💾 备份当前项目..."
if [ -d "heaven-backup" ]; then
    rm -rf heaven-backup
fi
cp -r heaven heaven-backup 2>/dev/null || true

# 2. 解压新项目文件
echo "📦 解压新项目文件..."
tar -xzf heaven-project.tar.gz

# 3. 重启容器
echo "🔄 重启容器..."
docker-compose -f docker-compose.server.yml restart web

# 4. 清理缓存
echo "🧹 清理缓存..."
sleep 10
docker exec heaven-web php artisan config:cache
docker exec heaven-web php artisan route:cache
docker exec heaven-web php artisan view:cache

echo "✅ 快速部署完成！"
EOF

chmod +x deploy-package/quick-deploy.sh

# 12. 生成部署说明文档
echo "📄 生成部署说明文档..."
cat > deploy-package/README.md << 'EOF'
# Heaven项目服务器部署包

## 📦 包含文件
- `heaven-web-image.tar.gz` - Docker镜像文件
- `heaven-project.tar.gz` - 项目源代码
- `docker-compose.server.yml` - 服务器部署配置
- `.env.production` - 生产环境配置
- `server-deploy.sh` - 完整部署脚本
- `quick-deploy.sh` - 快速部署脚本（仅更新代码）

## 🚀 部署步骤

### 首次部署
```bash
# 1. 上传整个deploy-package目录到服务器
scp -r deploy-package/ user@server:/path/to/deployment/

# 2. 登录服务器并执行部署
ssh user@server
cd /path/to/deployment/deploy-package/
chmod +x server-deploy.sh
./server-deploy.sh
```

### 快速更新（仅更新代码）
```bash
# 如果只是代码更新，使用快速部署
./quick-deploy.sh
```

## 🔧 部署后配置

1. **更新域名配置**
   编辑 `.env.production` 文件中的域名设置

2. **访问应用**
   - 后台管理: `http://server-ip:8081/admin`
   - 前台页面: `http://server-ip:8081`

## ⚠️ 注意事项

- 确保服务器已安装 Docker 和 Docker Compose
- 确保端口 8081 和 6379 可用
- 确保MySQL数据库可访问
- 建议在生产环境中配置SSL证书
EOF

# 13. 显示部署包信息
echo ""
echo "✅ 部署包生成完成！"
echo ""
echo "📁 部署包内容:"
ls -lah deploy-package/
echo ""
echo "📊 文件大小统计:"
du -h deploy-package/*
echo ""
echo "📋 使用说明:"
echo "1. 将整个 deploy-package/ 目录上传到服务器"
echo "2. 首次部署运行: ./server-deploy.sh"
echo "3. 代码更新运行: ./quick-deploy.sh"
echo "4. 详细说明请查看: deploy-package/README.md"
echo ""
echo "🎯 部署包已准备就绪，可以上传到服务器了！"
