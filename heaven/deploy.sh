#!/bin/bash

# Heaven Backend 部署脚本
# 使用方法: ./deploy.sh [环境] [操作]
# 环境: dev|staging|production
# 操作: deploy|update|restart|stop|logs

set -e

# 配置变量
ENVIRONMENT=${1:-production}
ACTION=${2:-deploy}
PROJECT_NAME="heaven"
COMPOSE_FILE="docker-compose.richarvey.yml"
CONTAINER_NAME="heaven-web"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_requirements() {
    log_info "检查系统要求..."

    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi

    log_info "系统要求检查通过"
}

# 环境配置
setup_environment() {
    log_info "设置 ${ENVIRONMENT} 环境..."

    # 复制环境配置文件
    if [ -f ".env.${ENVIRONMENT}" ]; then
        cp ".env.${ENVIRONMENT}" .env
        log_info "已复制 .env.${ENVIRONMENT} 到 .env"
    else
        log_warn ".env.${ENVIRONMENT} 文件不存在，请手动创建"
    fi

    # 设置目录权限（如果目录存在）
    if [ -d "storage" ]; then
        chmod -R 775 storage || log_warn "无法设置storage目录权限"
    fi
    if [ -d "bootstrap/cache" ]; then
        chmod -R 775 bootstrap/cache || log_warn "无法设置bootstrap/cache目录权限"
    fi

    log_info "环境设置完成"
}

# 构建和启动服务
deploy_services() {
    log_info "部署服务..."

    # 停止现有服务
    docker-compose -f $COMPOSE_FILE down

    # 构建镜像
    log_info "构建Docker镜像..."
    docker-compose -f $COMPOSE_FILE build --no-cache

    # 启动服务
    log_info "启动服务..."
    docker-compose -f $COMPOSE_FILE up -d

    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30

    # 检查服务状态
    docker-compose -f $COMPOSE_FILE ps

    log_info "服务部署完成"
}

# 更新应用
update_application() {
    log_info "更新应用..."

    # 拉取最新代码
    git pull origin main

    # 重新构建和启动
    docker-compose -f $COMPOSE_FILE up -d --build

    # 运行Laravel命令
    run_laravel_commands

    log_info "应用更新完成"
}

# 运行Laravel命令
run_laravel_commands() {
    log_info "运行Laravel命令..."

    # 等待数据库启动
    sleep 10

    # 安装依赖
    docker exec $CONTAINER_NAME composer install --no-dev --optimize-autoloader || log_warn "Composer install 失败"

    # 生成应用密钥
    docker exec $CONTAINER_NAME php artisan key:generate --force || log_warn "Key generate 失败"

    # 清除缓存
    docker exec $CONTAINER_NAME php artisan config:clear || true
    docker exec $CONTAINER_NAME php artisan cache:clear || true
    docker exec $CONTAINER_NAME php artisan view:clear || true
    docker exec $CONTAINER_NAME php artisan route:clear || true

    # 运行数据库迁移
    docker exec $CONTAINER_NAME php artisan migrate --force || log_warn "Migration 失败"

    # 创建存储链接
    docker exec $CONTAINER_NAME php artisan storage:link || log_warn "Storage link 失败"

    # 优化应用
    docker exec $CONTAINER_NAME php artisan config:cache || log_warn "Config cache 失败"
    docker exec $CONTAINER_NAME php artisan route:cache || log_warn "Route cache 失败"
    docker exec $CONTAINER_NAME php artisan view:cache || log_warn "View cache 失败"

    log_info "Laravel命令执行完成"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    docker-compose -f $COMPOSE_FILE restart
    log_info "服务重启完成"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    docker-compose -f $COMPOSE_FILE down
    log_info "服务已停止"
}

# 查看日志
show_logs() {
    log_info "显示服务日志..."
    docker-compose -f $COMPOSE_FILE logs -f
}

# 健康检查
health_check() {
    log_info "执行健康检查..."

    # 检查容器状态
    if docker-compose -f $COMPOSE_FILE ps | grep -q "Up"; then
        log_info "容器运行正常"
    else
        log_error "部分容器未正常运行"
        docker-compose -f $COMPOSE_FILE ps
        return 1
    fi

    # 检查Web服务
    if curl -f http://82.156.98.152:8081 > /dev/null 2>&1; then
        log_info "Web服务响应正常"
    else
        log_warn "Web服务可能未完全启动，请稍后再试"
    fi

    # 显示访问地址
    log_info "访问地址:"
    echo "  管理后台: http://82.156.98.152:8081/admin"
    echo "  H5前端:   http://82.156.98.152:8080"
    echo "  API接口:  http://82.156.98.152:8081/api"

    log_info "健康检查完成"
}

# 显示帮助信息
show_help() {
    echo "Heaven Backend 部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [环境] [操作]"
    echo ""
    echo "环境:"
    echo "  production  - 生产环境 (默认)"
    echo "  staging     - 测试环境"
    echo "  dev         - 开发环境"
    echo ""
    echo "操作:"
    echo "  deploy      - 完整部署 (默认)"
    echo "  update      - 更新应用"
    echo "  restart     - 重启服务"
    echo "  stop        - 停止服务"
    echo "  logs        - 查看日志"
    echo ""
    echo "示例:"
    echo "  $0                    # 生产环境完整部署"
    echo "  $0 production deploy  # 生产环境完整部署"
    echo "  $0 production restart # 重启生产环境服务"
    echo "  $0 production logs    # 查看生产环境日志"
    echo ""
    echo "访问地址:"
    echo "  管理后台: http://82.156.98.152:8081/admin"
    echo "  H5前端:   http://82.156.98.152:8080"
    echo "  API接口:  http://82.156.98.152:8081/api"
}

# 主函数
main() {
    # 检查是否请求帮助
    if [[ "$1" == "--help" || "$1" == "-h" || "$1" == "help" ]]; then
        show_help
        exit 0
    fi

    log_info "开始部署 Heaven Backend - 环境: ${ENVIRONMENT}, 操作: ${ACTION}"

    check_requirements

    case $ACTION in
        "deploy")
            setup_environment
            deploy_services
            run_laravel_commands
            health_check
            ;;
        "update")
            update_application
            health_check
            ;;
        "restart")
            restart_services
            ;;
        "stop")
            stop_services
            ;;
        "logs")
            show_logs
            ;;
        *)
            log_error "未知操作: $ACTION"
            echo "可用操作: deploy, update, restart, stop, logs"
            echo "使用 $0 --help 查看详细帮助"
            exit 1
            ;;
    esac

    log_info "操作完成!"
}

# 执行主函数
main "$@"
