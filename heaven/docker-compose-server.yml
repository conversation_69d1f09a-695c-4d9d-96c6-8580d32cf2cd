version: '3'

services:
  web:
    image: heaven-web-image
    container_name: heaven-web
    ports:
      - "8081:80"
    volumes:
      - ./heaven:/var/www/html
      - ./heaven/scripts/start.sh:/start.sh
      - ./heaven/scripts/php.ini:/usr/local/etc/php/conf.d/custom.ini
      - ./heaven/scripts/default.conf:/etc/nginx/http.d/default.conf
      - ./heaven/scripts/www.conf:/usr/local/etc/php-fpm.d/www.conf
    environment:
      - WEBROOT=/var/www/html/public
      - PHP_ERRORS_STDERR=1
      - RUN_SCRIPTS=1
      - REAL_IP_HEADER=1
      - PUID=1000
      - PGID=1000
      - SKIP_COMPOSER=false
      - SKIP_CHOWN=false
      - SCRIPTS_DIR=/start.sh
      - APP_ENV=local
      - DB_HOST=host.docker.internal
      - DB_PORT=3306
      - DB_DATABASE=heaven
      - DB_USERNAME=heaven
      - DB_PASSWORD=LKC5pLYaLGMaLDrf
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - redis
    restart: unless-stopped
    extra_hosts:
      - "heavan.local.com:127.0.0.1"
      - "host.docker.internal:host-gateway"
  redis:
    image: heaven-redis-image
    container_name: heaven-redis
    ports:
      - "63791:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped

volumes:
  redis-data: