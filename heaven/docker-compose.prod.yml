version: '3.8'

services:
  # Web应用服务
  web:
    build:
      context: .
      dockerfile: Dockerfile.richarvey
    container_name: heaven-web-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./storage:/var/www/html/storage
      - ./public/storage:/var/www/html/public/storage
      - ./bootstrap/cache:/var/www/html/bootstrap/cache
      - /etc/letsencrypt:/etc/letsencrypt:ro  # SSL证书
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - APP_URL=https://yourdomain.com
    depends_on:
      - mysql
      - redis
    networks:
      - heaven-network

  # MySQL数据库
  mysql:
    image: mysql:5.7
    container_name: heaven-mysql-prod
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/sql:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - heaven-network

  # Redis缓存
  redis:
    image: redis:alpine
    container_name: heaven-redis-prod
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - heaven-network

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: heaven-nginx-prod
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/sites:/etc/nginx/conf.d:ro
      - /etc/letsencrypt:/etc/letsencrypt:ro
    depends_on:
      - web
    networks:
      - heaven-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  heaven-network:
    driver: bridge
