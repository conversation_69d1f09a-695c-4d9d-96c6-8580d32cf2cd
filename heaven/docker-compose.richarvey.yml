version: '3.8'

services:
  # Web应用服务 (使用richarvey/nginx-php-fpm镜像)
  web:
    build:
      context: .
      dockerfile: Dockerfile.richarvey
    container_name: heaven-web
    restart: unless-stopped
    ports:
      - "8081:80"
    volumes:
      - ./storage:/var/www/html/storage
      - ./bootstrap/cache:/var/www/html/bootstrap/cache
    environment:
      - APP_ENV=${APP_ENV:-local}
      - APP_DEBUG=${APP_DEBUG:-true}
      - APP_URL=${APP_URL:-http://localhost:8081}
    depends_on:
      - redis
    networks:
      - heaven-network

  # Redis缓存
  redis:
    image: redis:alpine
    container_name: heaven-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - heaven-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      retries: 3
      timeout: 5s

volumes:
  redis_data:
    driver: local

networks:
  heaven-network:
    driver: bridge
