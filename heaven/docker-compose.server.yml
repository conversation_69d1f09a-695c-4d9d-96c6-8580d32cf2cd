version: '3.8'

services:
  # Web应用服务 (使用richarvey/nginx-php-fpm镜像)
  web:
    build:
      context: .
      dockerfile: Dockerfile.richarvey
    container_name: heaven-web
    restart: unless-stopped
    ports:
      - "8081:80"
    volumes:
      - ./storage:/var/www/html/storage
      - ./public/storage:/var/www/html/public/storage
      - ./bootstrap/cache:/var/www/html/bootstrap/cache
      # 添加crontab配置
      - ./scripts/crontab:/etc/cron.d/laravel-scheduler
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - APP_URL=http://*************:8081
      # 数据库配置 - 使用服务器本地MySQL
      - DB_CONNECTION=mysql
      - DB_HOST=*************
      - DB_PORT=3306
      - DB_DATABASE=heaven
      - DB_USERNAME=yunyitang
      - DB_PASSWORD=${DB_PASSWORD}
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      # 缓存配置
      - CACHE_DRIVER=redis
      - SESSION_DRIVER=redis
      - QUEUE_CONNECTION=redis
    depends_on:
      - redis
    networks:
      - heaven-network
    # 确保cron服务启动
    command: >
      sh -c "
        # 设置crontab权限
        chmod 0644 /etc/cron.d/laravel-scheduler &&
        # 启动cron服务
        service cron start &&
        # 启动主服务
        /start.sh
      "

  # Redis缓存
  redis:
    image: redis:alpine
    container_name: heaven-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - heaven-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      retries: 3
      timeout: 5s
      interval: 30s

volumes:
  redis_data:
    driver: local

networks:
  heaven-network:
    driver: bridge
