#!/bin/bash

# Heaven Docker镜像导出和部署脚本
# 使用方法: ./docker-export.sh [操作] [选项]
# 操作: export, import, deploy

set -e

# 配置
PROJECT_NAME="heaven"
COMPOSE_FILE="docker-compose.richarvey.yml"
EXPORT_DIR="./docker-images"
SERVER_IP="*************"
SERVER_USER="root"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Heaven Docker镜像导出和部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [操作] [选项]"
    echo ""
    echo "操作:"
    echo "  export    - 导出Docker镜像"
    echo "  import    - 导入Docker镜像"
    echo "  deploy    - 部署到服务器"
    echo "  package   - 打包项目文件"
    echo ""
    echo "选项:"
    echo "  --server-ip IP    - 服务器IP地址 (默认: $SERVER_IP)"
    echo "  --server-user USER - 服务器用户名 (默认: $SERVER_USER)"
    echo "  --export-dir DIR  - 导出目录 (默认: $EXPORT_DIR)"
    echo ""
    echo "示例:"
    echo "  $0 export                    # 导出所有镜像"
    echo "  $0 package                   # 打包项目文件"
    echo "  $0 deploy --server-ip *******  # 部署到指定服务器"
}

# 创建导出目录
create_export_dir() {
    if [ ! -d "$EXPORT_DIR" ]; then
        mkdir -p "$EXPORT_DIR"
        log_info "创建导出目录: $EXPORT_DIR"
    fi
}

# 导出Docker镜像
export_images() {
    log_info "开始导出Docker镜像..."
    create_export_dir

    # 获取当前使用的镜像
    local images=$(docker-compose -f $COMPOSE_FILE config | grep 'image:' | awk '{print $2}' | sort | uniq)

    if [ -z "$images" ]; then
        log_error "未找到要导出的镜像"
        return 1
    fi

    log_info "找到以下镜像:"
    echo "$images"
    echo ""

    # 导出每个镜像
    for image in $images; do
        local image_name=$(echo $image | tr '/' '_' | tr ':' '_')
        local export_file="$EXPORT_DIR/${image_name}.tar"

        log_info "导出镜像: $image -> $export_file"

        if docker save -o "$export_file" "$image"; then
            log_success "导出成功: $export_file"

            # 压缩镜像文件
            log_info "压缩镜像文件..."
            gzip -f "$export_file"
            log_success "压缩完成: ${export_file}.gz"
        else
            log_error "导出失败: $image"
        fi
    done

    # 创建镜像列表文件
    echo "$images" > "$EXPORT_DIR/images.list"
    log_success "镜像列表已保存到: $EXPORT_DIR/images.list"

    log_success "所有镜像导出完成!"
}

# 导入Docker镜像
import_images() {
    log_info "开始导入Docker镜像..."

    if [ ! -d "$EXPORT_DIR" ]; then
        log_error "导出目录不存在: $EXPORT_DIR"
        return 1
    fi

    # 查找所有压缩的镜像文件
    local image_files=$(find "$EXPORT_DIR" -name "*.tar.gz" -type f)

    if [ -z "$image_files" ]; then
        log_error "未找到要导入的镜像文件"
        return 1
    fi

    log_info "找到以下镜像文件:"
    echo "$image_files"
    echo ""

    # 导入每个镜像
    for image_file in $image_files; do
        log_info "导入镜像: $image_file"

        # 解压缩
        local tar_file="${image_file%.gz}"
        gunzip -c "$image_file" > "$tar_file"

        # 导入镜像
        if docker load -i "$tar_file"; then
            log_success "导入成功: $image_file"
            rm -f "$tar_file"  # 删除临时解压文件
        else
            log_error "导入失败: $image_file"
        fi
    done

    log_success "所有镜像导入完成!"
}

# 打包项目文件
package_project() {
    log_info "开始打包项目文件..."
    create_export_dir

    local package_file="$EXPORT_DIR/heaven-project.tar.gz"

    # 创建临时目录
    local temp_dir=$(mktemp -d)
    local project_dir="$temp_dir/heaven"

    # 复制项目文件
    log_info "复制项目文件..."
    rsync -av --exclude='.git' --exclude='node_modules' --exclude='vendor' --exclude='docker-images' . "$project_dir/" || cp -r . "$project_dir"

    # 清理不需要的文件
    log_info "清理不需要的文件..."
    cd "$project_dir"

    # 删除开发相关文件
    rm -rf .git
    rm -rf node_modules
    rm -rf vendor
    rm -rf storage/logs/*
    rm -rf storage/framework/cache/data/*
    rm -rf storage/framework/sessions/*
    rm -rf storage/framework/views/*
    rm -rf bootstrap/cache/*
    rm -rf docker-images

    # 创建必要的目录
    mkdir -p storage/logs/video
    mkdir -p storage/framework/cache/data
    mkdir -p storage/framework/sessions
    mkdir -p storage/framework/views
    mkdir -p bootstrap/cache

    # 设置权限
    chmod -R 755 storage bootstrap/cache

    cd - > /dev/null

    # 打包
    log_info "创建压缩包..."
    tar -czf "$package_file" -C "$temp_dir" heaven

    # 清理临时目录
    rm -rf "$temp_dir"

    log_success "项目打包完成: $package_file"
    log_info "包大小: $(du -h "$package_file" | cut -f1)"
}

# 部署到服务器
deploy_to_server() {
    log_info "开始部署到服务器: $SERVER_USER@$SERVER_IP"

    if [ ! -d "$EXPORT_DIR" ]; then
        log_error "导出目录不存在: $EXPORT_DIR"
        log_info "请先运行: $0 export && $0 package"
        return 1
    fi

    # 检查必要文件
    local project_file="$EXPORT_DIR/heaven-project.tar.gz"
    if [ ! -f "$project_file" ]; then
        log_error "项目包不存在: $project_file"
        log_info "请先运行: $0 package"
        return 1
    fi

    # 上传项目文件
    log_info "上传项目文件到服务器..."
    scp "$project_file" "$SERVER_USER@$SERVER_IP:/tmp/"

    # 上传Docker镜像
    log_info "上传Docker镜像到服务器..."
    local image_files=$(find "$EXPORT_DIR" -name "*.tar.gz" -not -name "heaven-project.tar.gz" -type f)
    if [ ! -z "$image_files" ]; then
        scp $image_files "$SERVER_USER@$SERVER_IP:/tmp/"
    fi

    # 在服务器上执行部署
    log_info "在服务器上执行部署..."
    ssh "$SERVER_USER@$SERVER_IP" << 'EOF'
        set -e

        echo "=== 服务器部署开始 ==="

        # 停止现有服务
        if [ -d "/root/heaven" ]; then
            echo "停止现有服务..."
            cd /root/heaven
            docker-compose -f docker-compose.richarvey.yml down || true
        fi

        # 解压项目文件
        echo "解压项目文件..."
        cd /root
        tar -xzf /tmp/heaven-project.tar.gz

        # 导入Docker镜像
        echo "导入Docker镜像..."
        for image_file in /tmp/*.tar.gz; do
            if [ "$image_file" != "/tmp/heaven-project.tar.gz" ]; then
                echo "导入: $image_file"
                gunzip -c "$image_file" | docker load
            fi
        done

        # 启动服务
        echo "启动服务..."
        cd /root/heaven
        docker-compose -f docker-compose.richarvey.yml up -d

        # 等待服务启动
        echo "等待服务启动..."
        sleep 10

        # 检查服务状态
        echo "检查服务状态..."
        docker-compose -f docker-compose.richarvey.yml ps

        # 清理临时文件
        echo "清理临时文件..."
        rm -f /tmp/*.tar.gz

        echo "=== 服务器部署完成 ==="
        echo "访问地址:"
        echo "  管理后台: http://*************:8081/admin"
        echo "  H5前端:   http://*************:8080"
EOF

    log_success "部署完成!"
}

# 主函数
main() {
    local action=${1:-help}

    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --server-ip)
                SERVER_IP="$2"
                shift 2
                ;;
            --server-user)
                SERVER_USER="$2"
                shift 2
                ;;
            --export-dir)
                EXPORT_DIR="$2"
                shift 2
                ;;
            *)
                if [ -z "$action" ] || [ "$action" = "help" ]; then
                    action="$1"
                fi
                shift
                ;;
        esac
    done

    case $action in
        export)
            export_images
            ;;
        import)
            import_images
            ;;
        package)
            package_project
            ;;
        deploy)
            deploy_to_server
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
