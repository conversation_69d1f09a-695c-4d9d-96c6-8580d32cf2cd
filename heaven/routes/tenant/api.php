<?php

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::group(['namespace' => 'App\Http\Controllers\Api', 'middleware' => 'throttle:api'], function ($router){
    $router->group(['prefix' => 'auth', 'middleware' => ['throttle:auth', 'check.device']], function ($router){
        $router->post('login', 'AuthController@login')->name('auth.login');
        $router->post('/login/sms_code', 'AuthController@login')->name('auth.login.sms_code');
        $router->post('/login/wechat', 'AuthController@loginByWechat')->name('auth.login.wechat');
    });

    // 发送
    Route::group(['prefix' => 'send', 'middleware' => 'throttle:send'], function ($router){
        $router->post('sms', 'SendController@sms')->name('send.sms');
    });

    // 信息
    Route::group(['prefix' => 'info'], function ($router){
        $router->get('banners', 'InfoController@banner')->name('info.banner');
        $router->get('location', 'InfoController@location')->name('info.location');
        $router->get('share', 'InfoController@share')->name('info.share'); //分享
        $router->get('wx-config', 'InfoController@wxConfig')->name('info.wx-config'); //获取微信配置信息
        $router->get('common-messages', 'InfoController@commonMessages')->name('info.common-messages'); //常用留言
    });

    // 轮播图
    $router->group(['prefix' => 'carousels'], function ($router){
        $router->get('/', 'CarouselController@index')->name('carousel.index'); // 获取轮播图列表
        $router->get('{id}', 'CarouselController@show')->name('carousel.show'); // 获取轮播图详情
    });

    // AI相关功能
    $router->group(['namespace' => 'AI', 'prefix' => 'ai'], function ($router) {
        // 视频
        $router->group(['prefix' => 'videos'], function ($router){
            $router->get('/', 'VideoController@index')->name('ai.video.index'); // 获取视频列表
            $router->get('{id}', 'VideoController@show')->name('ai.video.show'); // 获取视频详情
            $router->post('callback', 'VideoController@callback')->name('ai.video.callback'); // 视频生成回调
        });

        // 背景音乐
        $router->group(['prefix' => 'musics'], function ($router){
            $router->get('active-musics', 'MusicController@activeMusics')->name('ai.music.active'); // 获取活跃的背景音乐列表
        });

        // 轮播图
        $router->group(['prefix' => 'carousels'], function ($router){
            $router->get('/', 'CarouselController@index')->name('ai.carousel.index'); // 获取轮播图列表
            $router->get('{id}', 'CarouselController@show')->name('ai.carousel.show'); // 获取轮播图详情
        });

        // 产品
        $router->group(['prefix' => 'products'], function ($router){
            $router->get('/', 'ProductController@index')->name('ai.product.index'); // 获取产品列表
            $router->get('{id}', 'ProductController@show')->name('ai.product.show'); // 获取产品详情
        });
    });

    // 视频相关API
    $router->group(['prefix' => 'videos'], function ($router){
        // 不需要认证的回调接口
        $router->post('/callback', 'VideoController@handleCallback')->name('video.callback'); // 视频生成回调

        // 需要认证的接口
        $router->group(['middleware' => 'auth:sanctum'], function ($router){
            $router->get('/', 'VideoController@index')->name('video.index'); // 获取视频列表
            $router->get('{id}', 'VideoController@show')->name('video.show'); // 获取视频详情
            $router->post('/generate', 'VideoController@generate')->name('video.generate'); // 生成视频
            $router->delete('{id}', 'VideoController@destroy')->name('video.destroy'); // 删除视频
            $router->post('{id}/regenerate', 'VideoController@regenerate')->name('video.regenerate'); // 重新生成视频
            $router->get('{id}/status', 'VideoController@checkStatus')->name('video.check_status'); // 检查视频状态
        });
    });

    // 音乐相关API
    $router->group(['prefix' => 'musics'], function ($router){
        // 不需要认证的接口
        $router->get('/active-musics', 'MusicController@activeMusics')->name('music.active_musics'); // 获取活跃的背景音乐列表
        $router->get('/bgm-list', 'MusicController@bgmList')->name('music.bgm_list'); // 获取所有背景音乐列表
        $router->get('{id}', 'MusicController@show')->name('music.show'); // 获取背景音乐详情
        $router->get('/user-music', 'MusicController@getUserMusic')->name('music.get_user_music'); // 获取用户当前的背景音乐（临时移出认证）

        // 需要认证的接口
        $router->group(['middleware' => 'auth:sanctum'], function ($router){
            $router->post('/set-user-music', 'MusicController@setUserMusic')->name('music.set_user_music'); // 设置用户的背景音乐
        });
    });

    // 留言
    $router->group(['controller' => 'CommentController'], function ($router){
        $router->get('comment', 'index')->name('comment.index'); // 获取留言列表

        $router->group(['middleware' => 'auth:sanctum'], function ($router){
            $router->post('comment', 'store')->name('comment.store'); // 创建留言
            $router->delete('comment/{id}', 'destroy')->name('comment.destroy'); // 删除留言
        });
    });

    // 文章相关
    $router->group(['controller' => 'ArticleController', 'prefix' => 'articles'], function ($router){
        $router->get('/', 'index')->name('article.index'); // 获取文章列表
        $router->get('categories', 'categories')->name('article.categories'); // 获取文章列表
        $router->get('notice_tip', 'noticeAndTip')->name('article.notice_tip'); // 获取通知和公告
        $router->get('{id}', 'show')->name('article.show'); // 获取文章详情
    });

    // 商品相关
    $router->group(['controller' => 'GoodsController'], function ($router){
        $router->get('goods', 'index')->name('goods.index'); // 获取商品列表
        $router->get('goods/recharge', 'recharge')->name('goods.recharge.index'); // 获取充值套餐
        $router->get('goods/recharge/point', 'recharge')->name('goods.recharge.point.index'); // 获取充值套餐-云积分
        $router->get('goods/{goods_id}/xml', 'xml')->name('goods.xml'); // 商品xml数据
    });

    // 分享视频相关 - 与 H5 share 页面对应
    $router->group(['prefix' => 'videos'], function ($router){
        $router->get('shared/{id}', 'AI\SharedVideoController@shared')->name('video.shared'); // 获取分享的视频
        $router->get('user/{id}', 'AI\SharedVideoController@userVideos')->name('video.user'); // 获取用户的视频列表
    });

    // 背景音乐相关 - 与 H5 share 页面对应
    $router->group(['prefix' => 'musics'], function ($router){
        $router->get('active-musics', 'AI\MusicController@activeMusics')->name('music.active'); // 获取活跃的背景音乐列表
    });

    // 灵堂相关
    $router->group(['prefix' => 'spaces'], function (Router $router){
        Route::group(['middleware' => 'auth:sanctum'], function ($router){
            $router->post('/', 'SpaceController@store')->name('space.store'); // 灵堂入住
            //相册
            $router->post('{space_id}/photo', 'PhotoController@store')->name('space.photo.store');
            $router->delete('{photo_id}/photo', 'PhotoController@delete')->name('space.photo.delete');
            // 装饰 and 级别
            $router->get('levels', 'SpaceController@levels')->name('space.levels');
            $router->put('{space_id}/bgm', 'SpaceDecorationController@bgm')->name('bgm.update'); // 更新背景音乐
            $router->put('{space_id}/bg', 'SpaceDecorationController@bg')->name('bg.update'); // 更新背景

            // 收藏
            $router->get('collect', 'CollectController@index')->name('space.collect.index');
            $router->post('{space_id}/collect', 'CollectController@store')->name('space.collect.store');
            $router->delete('{space_id}/collect', 'CollectController@destroy')->name('space.collect.destroy');

            // 更新
            $router->put('{space_id}', 'SpaceController@update')->name('space.update');
        });

        //相册
        $router->get('{space_id}/photo', 'PhotoController@index')->name('space.photo.index');

        $router->get('/', 'SpaceController@indexOfType')->name('space.index.type'); // 获取灵堂列表
        $router->get('/index', 'SpaceController@index')->name('space.index'); // 获取灵堂列表
        $router->get('{space_id}', 'SpaceController@show')->name('space.show')->middleware('view.inc'); // 获取灵堂详情
        $router->get('{space_id}/xml', 'SpaceController@showXml')->name('space.show.xml'); // 获取灵堂详情xml
        $router->get('{space_id}/goods_log', 'GoodsController@log')->name('space.goods.goods_log'); // 获取灵堂供奉记录
        $router->get('{space_id}/goods_log_info', 'GoodsController@logInfo')->name('space.goods.goods_log_info'); // 获取灵堂供奉记录简略
        $router->get('{space_id}/ranking', 'RankController@spaceGoods')->name('rank.space_goods'); // 获取灵堂供奉排行
        // 长明灯
        $router->get('{space_id}/lights', 'SpaceLightController@lightList'); // 点亮状态
        $router->get('{space_id}/lights_log', 'SpaceLightController@lightLog'); // 点亮日志
        $router->get('lights/accesstoken', 'SpaceLightController@getAccessToken'); // 点亮排行
        $router->get('lights/recharge', 'SpaceLightController@recharge'); // 点亮套餐
    });

    // 云讣告相关
    $router->group(['prefix' => 'obituaries'], function ($router){
        Route::group(['middleware' => 'auth:sanctum'], function ($router){
            $router->post('/', 'ObituaryController@store')->name('obituary.store'); // 讣告创建
            $router->put('{obituary_id}', 'ObituaryController@update')->name('obituary.update');// 讣告更新
            $router->get('{obituary_id}/bill', 'ObituaryController@bill'); // 账单
        });

        $router->get('{obituary_id}', 'ObituaryController@show')->name('obituary.show'); // 获取讣告详情
        $router->get('{obituary_id}/logInfo', 'ObituaryController@logInfo'); // 送礼记录
    });

    $router->get('ranking', 'RankController@ranking')->name('rank.ranking'); // 获取所有排行
    $router->get('ranking/groups', 'RankController@rankingGroups')->name('rank.ranking_groups'); // 首页显示的排行

    // 获取所有灵堂排行
    $router->get('space/ranking', 'SpaceRankController@ranking')->name('space_rank.ranking');
    // 分组显示的灵堂排行（周、月）
    $router->get('space/ranking/groups', 'SpaceRankController@rankingGroups')->name('space_rank.ranking_groups');

    // 需要登陆
    Route::group(['middleware' => 'auth:sanctum'], function ($router){
        $router->group(['prefix' => 'auth', 'middleware' => 'throttle:auth'], function ($router){
            $router->get('logout', 'AuthController@logout')->name('auth.logout');// 退出
        });

        //签到
        $router->post('signin', 'SigninController@store')->name('signin.store');

        //文件上传
        $router->post('upload', 'UploadController@store')->name('upload.store');

        // 创建居民
        $router->group(['prefix' => 'dweller', 'controller' => 'DwellerController'], function ($router){
            $router->post('/', 'store')->name('dweller.store'); // 创建居民
            $router->get('create', 'create')->name('dweller.create'); // 创建居民前待入住的居民列表
            $router->get('{dweller_id}', 'show')->name('dweller.show'); // 居民详情
            $router->put('{dweller_id}', 'update')->name('dweller.update'); // 居民更新
        });

        // 微信
        $router->group(['prefix' => 'wechat'], function ($router){
            $router->put('openid', 'WechatController@wxAuthCallback')->name('user.update.openid');
            $router->get('auth_url', 'WechatController@getWxAuthUrl')->name('wechat.getWxAuthUrl');
        });

        // 用户分组
        $router->group(['prefix' => 'user'], function ($router){
            $router->get('/', 'UserController@show')->name('user.show'); //用户信息
            $router->get('dwellers', 'DwellerController@indexByUser')->name('user.dweller.index'); // 获取用户的居民列表
            $router->get('spaces', 'SpaceController@indexUser')->name('user.space.index'); // 获取用户的居民列表
            $router->get('obituaries', 'ObituaryController@indexUser'); // 我的云讣告

            $router->put('/', 'UserController@update')->name('user.update'); // 用户信息修改
            $router->put('reset_password', 'UserController@resetPassword')->name('user.reset_password'); // 修改手机号
            $router->put('bind/mobile', 'UserController@bindMobile')->name('user.bind.mobile');//绑定手机号
            $router->put('certification', 'UserController@certification')->name('user.certification');//实名认证

            //用户钱包
            $router->get('bill', 'UserMoneyController@bill')->name('money.bill');
            $router->get('money', 'UserMoneyController@show')->name('money.show');
        });

        // 钱包
        $router->group(['prefix' => 'money'], function ($router){
            $router->post('transfer', 'UserMoneyController@transfer')->name('money.transfer');
            $router->get('withdraw', 'UserMoneyController@withdraw')->name('money.withdraw'); // 提现记录
            $router->get('withdraw/sand_cloud', 'UserMoneyController@sandCloudLink')->name('money.withdraw.sand_cloud'); // 杉德云账户链接
            $router->get('withdraw/config', 'UserMoneyController@withdrawConfig')->name('money.withdraw.config'); // 提现配置
            $router->get('withdraw/{withdraw_id}', 'UserMoneyController@withdrawDetail')->name('money.withdraw.show'); // 提现记录
        });

        // 订单
        $router->group(['prefix' => 'order'], function ($router){
            $router->get('/', 'OrderController@index')->name('order.index'); // 订单列表
            $router->post('/', 'OrderController@create')->name('order.create'); // 创建订单
            $router->get('{order_no}', 'OrderController@show')->name('order.show'); // 订单详情
            $router->post('{order_no}/pay', 'OrderController@pay')->name('order.pay'); // 订单支付
            $router->put('{order_no}/cancel', 'OrderController@cancel')->name('order.cancel'); // 订单取消
        });

        // 装饰
        $router->group(['controller' => 'SpaceDecorationController'], function ($router){
            $router->get('bgm', 'bgmIndex')->name('bgm.index'); // 获取音乐列表
            $router->get('bg', 'bgIndex')->name('bg.index'); // 获取音乐列表
        });
    });
});
