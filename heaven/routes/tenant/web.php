<?php

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return 'Hello World';
});

// 健康检查端点
Route::get('/health', function () {
    try {
        // 检查数据库连接
        \Illuminate\Support\Facades\DB::connection()->getPdo();

        // 检查Redis连接
        \Illuminate\Support\Facades\Redis::ping();

        return response()->json([
            'status' => 'ok',
            'timestamp' => now()->toISOString(),
            'services' => [
                'database' => 'ok',
                'redis' => 'ok'
            ]
        ], 200);
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'timestamp' => now()->toISOString(),
            'error' => $e->getMessage()
        ], 500);
    }
});

// Tenancy integration sanctum
// Route::group(['prefix' => config('sanctum.prefix', 'sanctum')], function () {
//     Route::get('/csrf-cookie',[\Laravel\Sanctum\Http\Controllers\CsrfCookieController::class, 'show'])
//         // Use tenancy initialization middleware of your choice
//         ->middleware([
//             'universal',
//             'web',
//             \Stancl\Tenancy\Middleware\InitializeTenancyByDomain::class
//         ])
//         ->name('sanctum.csrf-cookie');
// });

Route::group(['namespace' => 'App\Http\Controllers\Web'], function (Router $router){
    $router->group(['prefix' => 'pay'], function (Router $router){
        // 支付宝
        $router->get('alipay/return', 'Notify\AliPayController@return')->name('alipay.return');
        $router->post('alipay/notify', 'Notify\AliPayController@notify')->name('alipay.notify');

        // 微信
        $router->any('wechat/notify', 'Notify\WechatController@notify')->name('wechat.notify');

        // 杉德支付
        $router->post('sandpay/notify', 'Notify\SandPayController@notify')->name('sandpay.notify');
        $router->post('sandpay/notify/account', 'Notify\SandPayController@notifyAccount')->name('sandpay.notify.account');
    });
});
