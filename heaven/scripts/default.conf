server {
    listen 80;
    server_name localhost;
    root /var/www/html/public;
    index index.php index.html;

    # 添加访问日志和错误日志配置
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log debug;

    # 添加调试信息
    add_header X-Debug-Path $document_root$fastcgi_script_name;
    add_header X-Debug-Uri $uri;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # 添加调试头信息
        add_header X-Debug-File $document_root$fastcgi_script_name;
        add_header X-Debug-Exists $document_root$fastcgi_script_name;
    }

    location ~ /\.ht {
        deny all;
    }
}
