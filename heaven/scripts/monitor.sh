#!/bin/bash

# 系统监控脚本
# 监控Docker容器状态、系统资源使用情况

COMPOSE_FILE="docker-compose.prod.yml"
LOG_FILE="/var/log/heaven-monitor.log"

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1" | tee -a $LOG_FILE
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1" | tee -a $LOG_FILE
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a $LOG_FILE
}

# 检查容器状态
check_containers() {
    log_info "检查容器状态..."
    
    local containers=("heaven-web-prod" "heaven-mysql-prod" "heaven-redis-prod")
    local failed_containers=()
    
    for container in "${containers[@]}"; do
        if ! docker ps --format "table {{.Names}}" | grep -q "^$container$"; then
            log_error "容器 $container 未运行"
            failed_containers+=($container)
        else
            log_info "容器 $container 运行正常"
        fi
    done
    
    if [ ${#failed_containers[@]} -gt 0 ]; then
        log_warn "尝试重启失败的容器..."
        docker-compose -f $COMPOSE_FILE up -d "${failed_containers[@]}"
    fi
}

# 检查系统资源
check_system_resources() {
    log_info "检查系统资源..."
    
    # 检查磁盘使用率
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ $disk_usage -gt 80 ]; then
        log_warn "磁盘使用率过高: ${disk_usage}%"
    fi
    
    # 检查内存使用率
    local mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ $mem_usage -gt 80 ]; then
        log_warn "内存使用率过高: ${mem_usage}%"
    fi
    
    # 检查CPU负载
    local cpu_load=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local cpu_cores=$(nproc)
    local cpu_usage=$(echo "$cpu_load * 100 / $cpu_cores" | bc -l | cut -d. -f1)
    
    if [ $cpu_usage -gt 80 ]; then
        log_warn "CPU使用率过高: ${cpu_usage}%"
    fi
    
    log_info "系统资源检查完成 - 磁盘: ${disk_usage}%, 内存: ${mem_usage}%, CPU: ${cpu_usage}%"
}

# 检查应用健康状态
check_application_health() {
    log_info "检查应用健康状态..."
    
    local health_url="http://localhost/health"
    local response=$(curl -s -o /dev/null -w "%{http_code}" $health_url)
    
    if [ "$response" = "200" ]; then
        log_info "应用健康检查通过"
    else
        log_error "应用健康检查失败，HTTP状态码: $response"
        
        # 尝试重启Web容器
        log_warn "尝试重启Web容器..."
        docker-compose -f $COMPOSE_FILE restart web
    fi
}

# 清理日志文件
cleanup_logs() {
    log_info "清理旧日志文件..."
    
    # 清理超过7天的日志
    find /var/log -name "*.log" -type f -mtime +7 -delete 2>/dev/null || true
    
    # 清理Docker日志
    docker system prune -f --volumes --filter "until=168h" 2>/dev/null || true
    
    log_info "日志清理完成"
}

# 备份数据库
backup_database() {
    log_info "备份数据库..."
    
    local backup_dir="/backup/mysql"
    local backup_file="heaven_$(date +%Y%m%d_%H%M%S).sql"
    
    mkdir -p $backup_dir
    
    docker exec heaven-mysql-prod mysqldump -u root -p${DB_ROOT_PASSWORD} heaven > "$backup_dir/$backup_file"
    
    if [ $? -eq 0 ]; then
        log_info "数据库备份成功: $backup_file"
        
        # 保留最近7天的备份
        find $backup_dir -name "heaven_*.sql" -type f -mtime +7 -delete
    else
        log_error "数据库备份失败"
    fi
}

# 发送告警通知
send_alert() {
    local message="$1"
    local webhook_url="${SLACK_WEBHOOK_URL:-}"
    
    if [ -n "$webhook_url" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"Heaven Backend Alert: $message\"}" \
            $webhook_url
    fi
    
    # 也可以发送邮件
    # echo "$message" | mail -s "Heaven Backend Alert" <EMAIL>
}

# 主监控函数
main() {
    log_info "开始系统监控 - $(date)"
    
    check_containers
    check_system_resources
    check_application_health
    
    # 每天凌晨执行清理和备份
    local hour=$(date +%H)
    if [ "$hour" = "02" ]; then
        cleanup_logs
        backup_database
    fi
    
    log_info "监控检查完成 - $(date)"
}

# 执行监控
main "$@"
