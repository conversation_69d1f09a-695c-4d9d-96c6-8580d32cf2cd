#!/bin/bash

# SSL证书设置脚本
# 使用Let's Encrypt获取免费SSL证书

DOMAIN=${1:-yourdomain.com}
EMAIL=${2:-<EMAIL>}

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ -z "$DOMAIN" ] || [ -z "$EMAIL" ]; then
    log_error "使用方法: $0 <domain> <email>"
    log_error "示例: $0 yourdomain.com <EMAIL>"
    exit 1
fi

log_info "为域名 $DOMAIN 设置SSL证书..."

# 安装Certbot
log_info "安装Certbot..."
sudo apt update
sudo apt install -y certbot python3-certbot-nginx

# 停止可能占用80端口的服务
log_info "停止可能冲突的服务..."
sudo systemctl stop nginx 2>/dev/null || true
docker stop heaven-nginx-prod 2>/dev/null || true
docker stop heaven-web-prod 2>/dev/null || true

# 获取证书
log_info "获取SSL证书..."
sudo certbot certonly --standalone \
    --email $EMAIL \
    --agree-tos \
    --no-eff-email \
    --domains $DOMAIN,www.$DOMAIN

if [ $? -eq 0 ]; then
    log_info "SSL证书获取成功!"
    
    # 设置自动续期
    log_info "设置证书自动续期..."
    echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
    
    log_info "SSL证书设置完成!"
    log_info "证书位置: /etc/letsencrypt/live/$DOMAIN/"
else
    log_error "SSL证书获取失败!"
    exit 1
fi
