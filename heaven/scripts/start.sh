#!/bin/bash

# Install Laravel dependencies
cd /var/www/html
composer install --no-interaction --no-dev --optimize-autoloader

# Set proper permissions
chown -R nginx:nginx /var/www/html
chmod -R 755 /var/www/html/storage

# Generate application key if not exists
if [ -z "$(grep -E '^APP_KEY=' .env)" ]; then
    php artisan key:generate
fi

# Clear caches
php artisan config:clear
php artisan cache:clear
php artisan view:clear

# Setup cron
crontab /etc/crontabs/root
crond -b -d 8

# Start supervisor to manage processes
/usr/bin/supervisord -n -c /etc/supervisord.conf
