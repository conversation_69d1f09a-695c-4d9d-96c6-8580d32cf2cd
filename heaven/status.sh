#!/bin/bash

# Heaven 项目状态检查脚本
# 使用方法: ./status.sh

set -e

# 配置
COMPOSE_FILE="docker-compose.richarvey.yml"
CONTAINER_NAME="heaven-web"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查容器状态
check_containers() {
    echo "==================== 容器状态 ===================="
    if docker-compose -f $COMPOSE_FILE ps 2>/dev/null; then
        log_success "容器状态查询成功"
    else
        log_error "无法查询容器状态"
        return 1
    fi
    echo ""
}

# 检查服务健康状态
check_health() {
    echo "==================== 服务健康检查 ===================="
    
    # 检查Web服务
    if curl -s -o /dev/null -w "%{http_code}" http://82.156.98.152:8081 | grep -q "200\|302"; then
        log_success "Web服务响应正常"
    else
        log_warning "Web服务可能异常"
    fi
    
    # 检查管理后台
    if curl -s -o /dev/null -w "%{http_code}" http://82.156.98.152:8081/admin | grep -q "200\|302"; then
        log_success "管理后台响应正常"
    else
        log_warning "管理后台可能异常"
    fi
    
    echo ""
}

# 检查数据库连接
check_database() {
    echo "==================== 数据库连接 ===================="
    
    if docker exec $CONTAINER_NAME php artisan tinker --execute="DB::connection()->getPdo(); echo 'Database connection OK';" 2>/dev/null | grep -q "Database connection OK"; then
        log_success "数据库连接正常"
    else
        log_error "数据库连接失败"
    fi
    echo ""
}

# 检查定时任务
check_schedule() {
    echo "==================== 定时任务状态 ===================="
    
    if docker exec $CONTAINER_NAME php artisan schedule:list 2>/dev/null; then
        log_success "定时任务配置正常"
    else
        log_error "定时任务配置异常"
    fi
    echo ""
}

# 检查视频处理状态
check_videos() {
    echo "==================== 视频处理状态 ===================="
    
    local stats=$(docker exec $CONTAINER_NAME php artisan tinker --execute="
    \$service = new \App\AI\Services\VideoStatusService();
    \$stats = \$service->getStatusStats();
    echo json_encode(\$stats, JSON_UNESCAPED_UNICODE);
    " 2>/dev/null | tail -1)
    
    if [ ! -z "$stats" ]; then
        echo "视频统计: $stats"
        log_success "视频状态查询成功"
    else
        log_error "视频状态查询失败"
    fi
    echo ""
}

# 检查日志
check_logs() {
    echo "==================== 最近日志 ===================="
    
    # 检查应用日志
    if [ -f "storage/logs/laravel.log" ]; then
        echo "最近的应用日志:"
        tail -5 storage/logs/laravel.log 2>/dev/null || log_warning "无法读取应用日志"
    fi
    
    # 检查视频日志
    local today=$(date +%Y-%m-%d)
    if [ -f "storage/logs/video/video-$today.log" ]; then
        echo ""
        echo "今日视频处理日志:"
        tail -5 "storage/logs/video/video-$today.log" 2>/dev/null || log_warning "无法读取视频日志"
    fi
    echo ""
}

# 显示访问信息
show_access_info() {
    echo "==================== 访问信息 ===================="
    echo "🌐 管理后台: http://82.156.98.152:8081/admin"
    echo "📱 H5前端:   http://82.156.98.152:8080"
    echo "🔗 API接口:  http://82.156.98.152:8081/api"
    echo ""
    echo "🔧 常用命令:"
    echo "  查看日志:   ./deploy.sh production logs"
    echo "  重启服务:   ./deploy.sh production restart"
    echo "  进入容器:   docker exec -it $CONTAINER_NAME bash"
    echo "  手动查询:   docker exec -it $CONTAINER_NAME php artisan video:query-status"
    echo ""
}

# 主函数
main() {
    echo "🚀 Heaven 项目状态检查"
    echo "时间: $(date)"
    echo ""
    
    check_containers
    check_health
    check_database
    check_schedule
    check_videos
    check_logs
    show_access_info
    
    log_success "状态检查完成！"
}

# 执行主函数
main "$@"
