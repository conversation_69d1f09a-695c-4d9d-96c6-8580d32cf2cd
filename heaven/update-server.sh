#!/bin/bash

# Heaven 服务器配置更新脚本
# 用于更新服务器上的docker-compose.yml配置

set -e

# 配置
SERVER_IP="*************"
SERVER_USER="root"
SERVER_PATH="/root/local"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Heaven 服务器配置更新脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [操作]"
    echo ""
    echo "操作:"
    echo "  update-config    - 更新服务器配置文件"
    echo "  restart-service  - 重启服务"
    echo "  check-cron      - 检查定时任务状态"
    echo "  help            - 显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 update-config"
    echo "  $0 restart-service"
}

# 更新服务器配置
update_server_config() {
    log_info "开始更新服务器配置..."
    
    # 备份现有配置
    log_info "备份现有配置..."
    ssh $SERVER_USER@$SERVER_IP "cd $SERVER_PATH && cp docker-compose.yml docker-compose.yml.backup.$(date +%Y%m%d_%H%M%S)"
    
    # 上传新的配置文件
    log_info "上传新的docker-compose配置..."
    scp docker-compose.server.yml $SERVER_USER@$SERVER_IP:$SERVER_PATH/docker-compose.yml
    
    # 上传crontab配置
    log_info "上传crontab配置..."
    ssh $SERVER_USER@$SERVER_IP "mkdir -p $SERVER_PATH/heaven/scripts"
    scp scripts/crontab $SERVER_USER@$SERVER_IP:$SERVER_PATH/heaven/scripts/
    
    # 上传其他必要的脚本
    log_info "上传部署脚本..."
    scp deploy.sh status.sh $SERVER_USER@$SERVER_IP:$SERVER_PATH/heaven/
    ssh $SERVER_USER@$SERVER_IP "chmod +x $SERVER_PATH/heaven/deploy.sh $SERVER_PATH/heaven/status.sh"
    
    log_success "配置文件更新完成"
}

# 重启服务
restart_service() {
    log_info "重启服务..."
    
    ssh $SERVER_USER@$SERVER_IP << 'EOF'
        cd /root/local
        
        # 停止现有服务
        echo "停止现有服务..."
        docker-compose down || true
        
        # 启动新服务
        echo "启动服务..."
        docker-compose up -d --build
        
        # 等待服务启动
        echo "等待服务启动..."
        sleep 15
        
        # 检查服务状态
        echo "检查服务状态..."
        docker-compose ps
        
        # 检查定时任务
        echo "检查定时任务..."
        docker exec heaven-web crontab -l || echo "Crontab not found"
        
        # 手动启动cron服务
        echo "启动cron服务..."
        docker exec heaven-web service cron start || true
        
        # 检查cron状态
        echo "检查cron状态..."
        docker exec heaven-web service cron status || true
EOF
    
    log_success "服务重启完成"
}

# 检查定时任务状态
check_cron_status() {
    log_info "检查定时任务状态..."
    
    ssh $SERVER_USER@$SERVER_IP << 'EOF'
        echo "=== 容器状态 ==="
        docker ps | grep heaven
        
        echo ""
        echo "=== Cron服务状态 ==="
        docker exec heaven-web service cron status || echo "Cron service not running"
        
        echo ""
        echo "=== Crontab配置 ==="
        docker exec heaven-web crontab -l || echo "No crontab found"
        
        echo ""
        echo "=== Laravel定时任务配置 ==="
        docker exec heaven-web php artisan schedule:list || echo "Schedule list failed"
        
        echo ""
        echo "=== 手动执行调度器 ==="
        docker exec heaven-web php artisan schedule:run
        
        echo ""
        echo "=== 检查日志 ==="
        docker exec heaven-web tail -10 /var/log/cron.log 2>/dev/null || echo "No cron log found"
EOF
    
    log_success "定时任务状态检查完成"
}

# 主函数
main() {
    local action=${1:-help}
    
    case $action in
        update-config)
            update_server_config
            ;;
        restart-service)
            restart_service
            ;;
        check-cron)
            check_cron_status
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
